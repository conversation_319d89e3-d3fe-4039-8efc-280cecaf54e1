package com.yunqu.cc.mixgw.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.http.HttpResp;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.utils.CsUtil;
import com.yunqu.cc.mixgw.utils.GVOCSignUtil;
import com.yunqu.cc.mixgw.utils.HttpClientUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import java.util.SortedMap;
import java.util.TreeMap;

/**
 * 调用内部质检接口
 * <AUTHOR>
 *
 */
public class QualityService extends IService{
	
    private static Logger logger = CommonLogger.getCommLogger("quality");
    private static Logger longTime = CommonLogger.getCommLogger("longTime");

	private String funcUrl = "";
	
	@Override
	public JSONObject invoke(JSONObject resqJson) throws ServiceException {
		String command = resqJson.getString("command");
		long startTime = System.currentTimeMillis();
		try {
			if("sensitiveWordsQuality".equals(command)){ //提交回访记录
				return sensitiveWordsQuality(resqJson);
			}else{
				JSONObject result = new JSONObject();
				result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
				result.put("respDesc", "不存在的command,请检查！");
				return result;
			}
		} finally {
			long endTime = System.currentTimeMillis();
			long elapsedTime = endTime - startTime;
			longTime.info(command+"接口"+elapsedTime);
		}
	}
	/**
	 * 敏感词实时质检接口
	 * @return
	 */
	public synchronized JSONObject sensitiveWordsQuality(JSONObject json) {
		funcUrl = Constants.getSensitiveWordsQuality();
		JSONObject result = new JSONObject();
		JSONObject body=json.getJSONObject("params");
		try{
			logger.info("敏感词实时质检接口,url="+funcUrl+"参数"+body.toJSONString());
			String post = HttpClientUtil.postBodyTimeout(funcUrl,body.toJSONString(),"UTF-8",8000);
			logger.info("敏感词实时质检接口,url="+funcUrl+"参数"+body.toJSONString()+",结果-》："+post);

			JSONObject parseObject = JSON.parseObject(post);
			if("0".equals(parseObject.get("result"))||"0000".equals(parseObject.get("result"))){
				logger.info("敏感词实时质检接口成功,url,url="+funcUrl+",结果："+JSONObject.toJSONString(parseObject));
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respData", parseObject);
				result.put("respDesc", "操作成功");
			}else{
				logger.info("敏感词实时质检接口失败,url="+funcUrl+",结果：");
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", parseObject.get("message"));
			}

		}catch(Exception e){
			logger.info("敏感词实时质检接口,url="+funcUrl+",原因："+e.getMessage());
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "操作失败");
		}
		return result;
	}
}
