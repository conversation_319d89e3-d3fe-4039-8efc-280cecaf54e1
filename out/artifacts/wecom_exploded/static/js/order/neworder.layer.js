var	 checkNum=0;
var orderCreatePerson;
var _faultServiceContent = "";
/**
 * 按钮及弹框js
 */
var orderpopup={
		//preOperate : '',  //上一次操作(记录提交、复制、暂存)
		queryOrder : function(){
			var phone = orderVue.$data.formData.customerMobilephone1;
		   	popup.layerShow({type:2,title:'电商订单查询',area:['900px','700px'],moveOut:true,offset:'20px'},"/online/servlet/page?action=ecoOrder&callback=1&callbackphone="+phone);
	   	},purchaseHistory : function(){
			var phone = orderVue.$data.formData.customerMobilephone1;
		   	popup.layerShow({type:2,title:'购买记录查询',area:['1300px','850px'],moveOut:true,offset:'20px'},"/neworder/pages/access/purchase-history.jsp?callback=1&callbackphone="+phone);
	   	},
	   	salesCenter:function(){
			var orgCode = orderVue.$data.formData.orgCode;
			var branchName = orderVue.$data.formData.branchName;
			window.open("/neworder/pages/access/sales_center.jsp?branchName="+branchName+"&orgCode="+orgCode);
	   	},
	   	contactOrderSearch(){
	   		window.open("/neworder/pages/access/order-list.jsp");
	   	},
	   	prolongservarchives:function(){
			var serviceCustomerTel = orderVue.$data.formData.customerMobilephone1;
			window.open("/neworder/pages/access/prolongservarchives.jsp?serviceCustomerTel="+serviceCustomerTel);
	   	},
	   	hitchTree:function(){
	   		var orgCode="";
	   		if($("#orgCode").val()!=""){
		   		 orgCode=$("#orgCode").find("option:selected").text();
	   		}
	   		var pageFull =  popup.layerShow({
				type : 2,
				title : "故障树查询",
				offset : '-100px',
				area : [ '0px', '0px' ]
			}, "/slinfogw/jump?type=httpUrl&userAcc=liangjc&httpUrl=/fault?categoryName="+orgCode, {});
		    layer.full(pageFull);
	   	},	
		
	   	insideBarcode:function(){
	   		var orgCode = orderVue.$data.formData.orgCode;
	   		window.open("/neworder/pages/access/insideBarcode_list.jsp?&orgCode="+orgCode);
	   	},
	   	servicePolicyTab:function(){
	   		var params = encodeURI("orgCode="+orderVue.$data.formData.orgCode
   					+"&prodCode="+orderVue.$data.formData.prodCode+"&prodName="+orderVue.$data.formData.prodName
   					+"&brandCode="+orderVue.$data.formData.brandCode+"&brandName="+orderVue.$data.formData.brandName
   					+"&productModel="+orderVue.$data.formData.productModel+"&productCode="+orderVue.$data.formData.productCode);
	   		window.open("/neworder/pages/access/service-policy-tab.jsp?"+params);
	   	},
	   	chargedetails:function(){
	   		var customerMobilephone1 = orderVue.$data.formData.customerMobilephone1;
	   		window.open("/neworder/pages/access/chargedetails.jsp?&customerMobilephone1="+customerMobilephone1);
	   	},
	   	engineerInfo:function(){
	   		var customerMobilephone1 = $("#customerMobilephone1").val();
	   		popup.layerShow({type:2,title:'工程师位置查询',area:['1300px','700px'],moveOut:true,offset:'20px',shadeClose:false},"/neworder/pages/access/service-order-list.jsp?&phone="+customerMobilephone1);
	   	},
	   	queryStore : function(){
	   		var areaName = orderVue.$data.formData.areaName;
	   		var phone = orderVue.$data.formData.customerMobilephone1;
	   		var param = encodeURI("areaName="+areaName+"&phone="+phone);
		   	window.open("/neworder/pages/access/store-list.jsp?sms=1&" + param);
	   	},
	   
	   	queryWebsite : function(){
	   		var params = encodeURI("&areaNum="+orderVue.$data.formData.areaNum+"&orgCode="+orderVue.$data.formData.orgCode
	   					+"&prodCode="+orderVue.$data.formData.prodCode+"&prodName="+orderVue.$data.formData.prodName
	   					+"&areaCode="+orderVue.$data.formData.areaCode+"&areaName="+orderVue.$data.formData.areaName
	   					+"&unitName="+orderVue.$data.formData.unitName+"&phone="+orderVue.$data.formData.customerMobilephone1);
		   	window.open("/neworder/pages/access/website-list.jsp?sms=1&ok="+params+"&type=1");
	   	},
	   	//服务政策
	   	servicePolicy : function(){
	   		var open = orderpopup.checkWindowOpened("服务政策查询");
			if(open == false){
				layer.msg("服务政策查询已经存在，且处于最小化状态！",{icon:5});
				return ;
			}
	   		var productModel= orderVue.$data.formData.productModel ; 
	   		if(!checkNull(productModel)){
	   			productModel = productModel + " 服务政策";
	   		}else{
	   			var orgCode= orderVue.$data.formData.orgCode ; 
	   			if(!checkNull(orgCode)){
	   				productModel = $("#orgCode").find("option[value='"+orgCode+"']").text() + " 服务政策";
	   			}
	   		}
	   		popup.layerShow({type:2,title:'服务政策查询',area:['860px','600px'],offset:'20px',shade:0,maxmin:2,moveOut:true,shadeClose:false},"/neworder/pages/access/service-policy.jsp?productModel="+productModel);
	   	},
	   	queryProduct : function(){
//	   		if(!orderpopup.checkBrandCode()){  return;  }//不校验2018-12-20
	   		var brandCode= orderVue.$data.formData.brandCode ; 
	   		var orgCode= orderVue.$data.formData.orgCode ; 
	   		var prodCode= orderVue.$data.formData.prodCode ; 
	   		var productModel= orderVue.$data.formData.productModel ; 
	   		var param = encodeURI("brandCode="+brandCode+"&orgCode="+orgCode+"&prodCode="+prodCode+"&productModel="+productModel+"&productModelId=productModel&productCodeId=productCode&callback=1&type=1");
		   	popup.layerShow({type:2,title:'产品型号查询',area:['1050px','650px'],offset:'20px',shadeClose:false},"/neworder/pages/access/product-list.jsp?"+param);
	   	},
	   	
	   	queryAllProduct : function(){
	   		var open = orderpopup.checkWindowOpened("产品查询");
			if(open == false){
				layer.msg("产品查询已经存在，且处于最小化状态！",{icon:5});
				return ;
			}
	   		popup.layerShow({type:2,title:'产品查询',area:['860px','650px'],offset:'20px',shade:0,maxmin:2,moveOut:true,shadeClose:false},"/neworder/pages/access/product-all-list.jsp");
	   	},
	   	
	   	addAdvise : function(){
	   		var param = orderpopup.getVocNeedData(2);
	   		param=param+"&sessionId="+orderVue.$data.formData.sessionId
		   	popup.layerShow({type:2,title:'新增产品服务建议',area:['1000px','650px'],offset:'20px',shadeClose:false},"/advice/servlet/msg?action=usreInfoAddByOrder"+param);
	   	},
	   
	   	applySpecial : function(){
	   		if(checkNull(orderVue.$data.formData.contactUserRequireId)){
	   			orderVue.$message("售后不存在该诉求，请先提及诉求信息！");
	   			return;
	   		}
	   		var param = orderpopup.getVocNeedData(3);
		   	popup.layerShow({type:2,title:'特批资源处理',area:['840px','720px'],offset:'10px',shadeClose:false},"/neworder/pages/access/special-apply.jsp"+param);
	   	},
	  
	   	surname : function(){
	   		//设置id避免重复弹出
		   	popup.layerShow({type:2,title:'千家姓',area:['800px','540px'],offset:'20px',shadeClose:false,id:"surNameLayer"},"/neworder/pages/access/surname-list.jsp?callback=1");
	   	},
	   

	   	productType : function(){
	   		var prodName = orderVue.$data.formData.prodName;
		   	popup.layerShow({type:2,title:'产品品类',area:['860px','700px'],offset:'20px',shadeClose:false},
		   			"/neworder/pages/access/product-type.jsp?orgCode=orgCode&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName&callback=1" +
		   					"&productName="+prodName+"&org="+_orgCode);
	   	},
	   
	   	shop : function(){
	   		if(!orderpopup.checkProductCode()) { return;}
	   		var storeName = orderVue.$data.formData.contactOrderSaleUnitName ;
	   		var orgCode= 'CS001'; ///orderVue.$data.formData.orgCode; 售后销售单位的主体全是CS001
	   		var param = encodeURI("&orgCode="+orgCode+"&storeName="+storeName);
		   	popup.layerShow({type:2,title:'销售单位',area:['100%','600px'],offset:'20px',shadeClose:false},"/neworder/pages/access/shop-list.jsp?callback=1&storeCodeId=contactOrderSaleUnitCode&storeNameId=contactOrderSaleUnitName"+param);
	   	},
	   
	   	website : function(){
	   		if(!orderpopup.checkAreaCode()){ return ; }
	   		if(!orderpopup.checkProductCode()){ return ; }
	   		if(!orderpopup.checkServiceSubTypeCode()){ return ; }
	   		var areaCode= orderVue.$data.formData.areaCode;
	   		var prodCode= orderVue.$data.formData.prodCode;
	   		var serviceSubTypeCode= orderVue.$data.formData.serviceSubTypeCode;
	   		var orgCode= orderVue.$data.formData.orgCode; 
	   		var param = encodeURI("&orgCode="+orgCode+"&prodCode="+prodCode+"&areaCode="+areaCode+"&serviceSubTypeCode="+serviceSubTypeCode);
		   	popup.layerShow({type:2,title:'用户指定网点查询',area:['100%','650px'],offset:'20px',shadeClose:false},"/neworder/pages/access/website-query.jsp?callback=1&unitCodeId=unitCode&unitNameId=unitName"+param);
	   	},
	    
	   	queryServer : function(opType){
	   		if(!orderpopup.checkBrandCode()) { return ; }
	   		var orgCode= orderVue.$data.formData.orgCode; 
	   		var prodCode= orderVue.$data.formData.prodCode;
	   		var laseServiceItem= $("#contactOrderServTypeCode").val();
	   		var text= "";//带入搜索框的内容
	   		if(opType!=null&&opType==1){
		   		 text= $("#contactOrderSerItem2Name").val();
	   		}
	   		var tip = orderpopup.checkCDTime(laseServiceItem);
	   		var isExistUrge = orderpopup.checkCDRequire();
	   		if(tip != false){
	   			layer.confirm(tip ,{icon: 3, title:"提示",shadeClose:false},function(index){
	   				popup.layerShow({type:2,title:'服务请求',area:['400px','450px'],offset:'20px',shadeClose:false},"/neworder/pages/access/service-query.jsp?callback=1&orgCode="+orgCode+"&prodCode="+prodCode+"&laseServiceItem="+laseServiceItem+"&isExistUrge="+isExistUrge+"&text="+text);
	   				layer.close(index);
	   			},function(index){
	   				layer.close(index);
	   			});
	   		}else{
	   			popup.layerShow({type:2,title:'服务请求',area:['400px','450px'],offset:'20px',shadeClose:false},"/neworder/pages/access/service-query.jsp?callback=1&orgCode="+orgCode+"&prodCode="+prodCode+"&laseServiceItem="+laseServiceItem+"&isExistUrge="+isExistUrge+"&text="+text);
	   		}
	   		
	   	},
	   	//用户详细信息
	   	userinfo : function(){ 
	   		
	   		var open = orderpopup.checkWindowOpened("用户信息");
			if(open == false){
				layer.msg("用户信息已经存在，且处于最小化状态！",{icon:5});
				return ;
			}
	   		var customerCode = orderVue.$data.formData.customerCode;
		   	popup.layerShow({type:2,title:'用户信息',area:['900px','700px'],offset:'20px',shade:0,maxmin:2,shadeClose:false},"/neworder/pages/access/userinfo.jsp?customerCode="+customerCode);
	   	},
	   	
	   	serviceOrderMark : function(obj){
	   		var index = 0;
	   		var counterfeitFlag = obj.counterfeitFlag || "";
	   		var orgCode = obj.orgCode;
	   		var serviceOrderNo = obj.serviceOrderNo;
	   		var contactOrderNo = obj.contactOrderNo;
	   		var params ="?callback=1&serviceOrderNo="+serviceOrderNo+"&orgCode="+orgCode+"&contactOrderNo="+contactOrderNo+"&counterfeitFlag="+counterfeitFlag+"&index="+index;
	   		popup.layerShow({type:2,title:'服务单虚假标记',area:['100%','320px'],offset:'20px',shadeClose:false},"/neworder/pages/access/service-sheet-mark.jsp"+params);
	   	},
	   	
	   	ccunifyExplain : function(data){
	   		var phone = orderVue.$data.formData.customerMobilephone1;
	   		if(checkNull(phone)){
	   			return;
	   		}
	   		var data = {};
			data.customerTel = phone;  
			data.unifyExplainStatus = '12';
		   	//popup.layerShow({type:2,title:'统一口径',area:['800px','500px'],offset:'20px',shadeClose:false},"/neworder/pages/caliber/caliber-access-list.jsp?phone="+phone);
			ajax.remoteCall(ordercons.base + "/servlet/caliber?query=ccunifyExplain" ,data ,function(result){
				if(result.state == '1'){
					var data = result.data;
					if(data && data.total > 0) {
						var text = orderpopup.getUnifyExplainHtml(data.data)
						layer.open({type: 1,
							  title:['统一口径','background-color:#55a7f1;font-weight:bold;font-size:18px;color:#fff'],
							  anim: 2,
							  closeBtn : 0,
							  btnAlign : 'c',
							  shade : 0,
							  maxmin : true,
							  shadeClose : false,
							  area: ['650px', '300px'],
							  content: text
							});
					}
				}
			});
	   	},
	   	//假性故障
	   	excludedFault : function(content){
	   		_faultServiceContent=JSON.stringify(content);
		   	popup.layerShow({type:2,title:'假性故障',area:['100%','500px'],offset:'20px',shadeClose:false},"/neworder/pages/access/excluded-fault.jsp");
	   	},
	   	checkProductCode : function(){
	   		var prodCode= orderVue.$data.formData.prodCode;
	   		if(checkNull(prodCode)){
	   			orderVue.$message("产品品类不得为空，请选择！");
	   			return false;
	   		}
	   		return true;
	   	},
	   	checkServiceSubTypeCode : function() {
	   		var serviceSubTypeCode= orderVue.$data.formData.contactOrderSerItem2Code;
	   		if(checkNull(serviceSubTypeCode)){
	   			orderVue.$message("服务请求未选择完整，请选择！");
	   			return false;
	   		}
	   		return true;
	   	},
	   	checkAreaCode : function() {
	   		var areaCode= orderVue.$data.formData.areaNum;
	   		if(checkNull(areaCode)){
	   			orderVue.$message("区域名称不得为空，请选择！");
	   			return false;
	   		}
	   		return true;
	   	},
	   	checkBrandCode : function() {
	   		var brandCode= orderVue.$data.formData.brandCode;
	   		if(checkNull(brandCode)){
	   			orderVue.$message("请先选择产品品牌！");
	   			return false;
	   		}
	   		return true;
	   	},
	   	checkOrgCode : function() {
	   		var orgCode= orderVue.$data.formData.orgCode;
	   		if(checkNull(orgCode)){
	   			orderVue.$message("请先选择产品主体！");
	   			return false;
	   		}
	   		return true;
	   	},
	   	//检查催单时间
	   	checkCDTime : function(servTypeCode) {
	   		if(ordercons.baseRequireObj != null){
	   			var time = ordercons.baseRequireObj.requireCreateDate;
	   			var requireId = ordercons.baseRequireObj.contactUserRequireId
		   		if(servTypeCode == 'CD' && !checkNull(time) && !checkNull(requireId)){
		   			var nowTime = getNowTime();
		   			var diff = diffDateTime(time,nowTime);
		   			if(diff < 3600000) {
		   				return "提示：当前时间在建单1小时内，请选择【用户1小时内来电催】";
		   			}else if(diff < 3600000*24){
		   				return "提示：当前时间在建单24小时内，请勿选择【超24小时未与用户预约上门时间】";
		   			}
		   		}
	   		}
	   		return false;
	   	},
	    //检查一小时内催单类服务请求
	   	checkCDRequire : function() {
	   		if(ordercons.urgeRequireBaseList != null && ordercons.urgeRequireBaseList.length > 0){
	   			for(var k in ordercons.urgeRequireBaseList){
	   				var item = ordercons.urgeRequireBaseList[k];
	   				if(ordercons.urgeRequireList.indexOf(item.code) >= 0 && !checkNull(item.time)){
	   					var nowTime = getNowTime();
			   			var diff = diffDateTime(item.time,nowTime);
			   			if(orderpopup.checkCDorderId(item.contactProductInfoId)&&diff < 3600000){
				   				return true;
			   			}
	   				}
	   			}
	   		}
	   		return false;
	   	},
	   	checkCDorderId:function(contactProductInfoId){
	   		//催单判断是否是同一单就行催单
	   		var checked = $("input[name='custRequireList_checkbox']:checked");
			if (checked!=null&&$(checked).attr("data-id")==contactProductInfoId) {
				return true;
			}
	   		return false;
	   	},
	   	//提交小结单
	   	submit_online:function(t){
	   		var synOrder = false;//更新ecm工单
			var synContent = false;//更新卖家备注
			//咨询类别
			var consultType = JSON.parse(JSON.stringify(orderVue.$data.formData.consultType));
			var disposeType = orderVue.$data.formData.disposeType;
			var resolvedState = "2";
			if(disposeType==10){//解决
				resolvedState = "1";
			}else if(!t&&disposeType==11){
				layer.alert("处理方式为【派单】需提交售后工单跟进！请提交售后工单，系统会自动创建小结单！",{icon: 0});
				return;
			}else if(!t&&disposeType==12){
				layer.alert("转电商类问题需提交售后工单跟进！请提交售后工单，系统会自动创建小结单！",{icon: 0});
				return;
			}else if(disposeType==12||disposeType==13||disposeType==15){//转电商、转其他、转全网
				resolvedState = "3";
			}
			//紧急程度
			var urgentLevel = orderVue.$data.formData.urgentLevel;
			var l = "";
			if(urgentLevel==10){
				l = "05"
			}else if(urgentLevel==11){
				l = "03"
			}else if(urgentLevel==12){
				l = "01"
			}
			var param = {};
			param["C_OL_CONSULT_ORDER.BRAND_CODE"]= orderVue.$data.formData.brandCode;
			param["C_OL_CONSULT_ORDER.BRAND_NAME"] = orderVue.$data.formData.brandName;
			//param["C_OL_CONSULT_ORDER.CHANNEL_CODE"] = "wecom_999";
			param["C_OL_CONSULT_ORDER.CUST_NAME"] = orderVue.$data.formData.customerName;
			param["C_OL_CONSULT_ORDER.NIKE_NAME"] = orderVue.$data.formData.custNickname;
			// 确保能获取到custUserId，如果获取不到则给出提示
			if(typeof(custUserId) === "undefined" || custUserId === ""){
				// 尝试从父页面获取custUserId
				try{
					var parentCustUserId = window.parent.custUserId;
					if(typeof(parentCustUserId) !== "undefined" && parentCustUserId !== ""){
						custUserId = parentCustUserId;
					}
				}catch(e){
					// 跨域访问异常，忽略
				}
			}
			param["C_OL_CONSULT_ORDER.CUST_USERID"] = custUserId;
			param["C_OL_CONSULT_ORDER.CUST_PHONE"] = orderVue.$data.formData.customerMobilephone1;
			param["C_OL_CONSULT_ORDER.ID"] = "";
			param["C_OL_CONSULT_ORDER.NAVI_NAME"] = "企业微信";
			param["C_OL_CONSULT_ORDER.ORDER_CONTENT"] = orderVue.$data.formData.contactOrderServiceDescribe;
			param["C_OL_CONSULT_ORDER.ORDER_FLAG"] = l;
			param["C_OL_CONSULT_ORDER.ORDER_NO"] = orderVue.$data.formData.sourceOrderCode;
			param["C_OL_CONSULT_ORDER.ORG_CODE"] = orderVue.$data.formData.orgCode;
			param["C_OL_CONSULT_ORDER.ORG_CODE_NAME"] = orderVue.$data.orgCodeOptions[orderVue.$data.formData.orgCode];
			param["C_OL_CONSULT_ORDER.PROD_CODE"] = orderVue.$data.formData.prodCode;
			param["C_OL_CONSULT_ORDER.PROD_CODE_NAME"] = orderVue.$data.formData.prodName;
			param["C_OL_CONSULT_ORDER.RESOLVED_STATE"] = resolvedState;
			param["C_OL_CONSULT_ORDER.SHOP_NAME"] = "";
			param.CONSULT_TYPE=1;
			param.UNION_SESSION_ID=1;
			param.CONSULT_SESSION_ID = custUserId +"-"+ dateFormat(new Date(), "yyyyMMdd");
			param.orderTypeArr=consultType.join();
			param.initaccount="";
			param.ecmOrder="";
			param.synOrder=synOrder;
			param.synContent=synContent;
			param.content=orderVue.$data.formData.contactOrderServiceDescribe;
			param.workId="";//美云销客服工单id
			param.orderId="";//美云销FoNo
			param.flag="";
			ajax.remoteCall("/online/servlet/order?action=NewSaveOrUpdate",param,function(data) {
				if(data.status=="OK"){
					if(!t){
						layer.alert(data.msg,{icon: 1},function(index){
							orderfunc.cleanFormData();
							layer.close(index);
						});
					}
				}else{
					let m = "";
					if(!t){
						m = "提交小结单出错:";
					}
					layer.msg(m+data.msg,{icon: 5});
		        }
			});
	   	},
	   	
	   	
	 	//提交
	 	submit_click : function() {
			//开始提交
	 		if(ordercons.isDoingSubmit){
	 			layer.alert("正在做提交或暂存操作，无法重复执行...");
	 			return;
	 		}
//	 		//校验姓名
//	 		if(!orderfunc.checkSurname(orderVue.$data.formData.customerName)){
//	 			return;
//	 		}
//	 		//校验手机号
//	 		if(!orderfunc.checkPhoneNumber(orderVue.$data.formData.customerMobilephone1)){
//	 			return;
//	 		}
	 		
	 		if(!orderfunc.checkAllContactorderservicedescribe()){//校验服务描述是否存存在xx
	 			return;
	 		}
	 		if(!orderfunc.checkContactorderservicedescribe()){//校验服务描是否为xx
	 			return;
	 		}
	 		if(orderVue.$data.appointMinDate){
	 			let minDate = orderVue.$data.appointMinDate;
	 			let appointDate = orderVue.$data.formData.appointDate;
	 			if(new Date(minDate) > new Date(appointDate)){
	 				layer.alert("预约日期限制为" + minDate + "及之后，请重新选择!");
	 				return;
	 			}
	 		}
	 		ordercons.isDoingSubmit = true;

	 		if(!orderpopup.checkCustForm()){ //提交用户信息校验
	 			ordercons.isDoingSubmit = false;
	 			return;
	 		}
	 		if(orderVue.$data.formData.disposeType=='12' && orderVue.$data.formData.ecmOrder==''){ //提交时，校验电商订单信息提示重选
	 			layer.alert("订单信息已清空，请重新从【电商订单查询】中选择订单信息！");
	 			ordercons.isDoingSubmit = false;
	 			return;
	 		}
	 		//获取vue中数据
	 		var temp = JSON.parse(JSON.stringify(orderVue.$data.formData));
	 		//获取当前诉求集合的索引
	 		var rIndex = orderVue.$data.formData.requireListIndex;
	 		var contactOrderStatus = temp.contactOrderStatus ;

	 		//只有一个诉求信息，且未添加到诉求列表，提交诉求信息校验
	 		if((rIndex == '-1' && orderfunc.custRequireList.length == 0) ||
	 			(rIndex == '-1' && orderfunc.custRequireList.length > 0 && contactOrderStatus == '' && !orderpopup.checkRequireFormNull(true))){
	 			var ret = orderpopup.checkRequireForm(temp);
	 			if(ret == true){
	 				orderpopup.checkRequireFrom(false); //校验后添加诉求集合
	 			}else{
	 				layer.alert(ret);
	 				ordercons.isDoingSubmit = false;
	 				return;
	 			}
	 		}
	 		var baseSize =  ordercons.baseRequireSize ;
	 		var serviceTypeCode = temp.contactOrderServTypeCode;
	 		//服务单追加投诉、补充单
	 		if(rIndex == '-1' && ordercons.preOperate != 'copy' && baseSize > 0 &&  contactOrderStatus !='10' && !orderpopup.checkRequireFormNull()){
	 			var ret = orderpopup.checkRequireForm(temp);
	 			if(ret == true){
	 				var checkR = orderpopup.checkRequireFrom(false); //校验后添加诉求集合
	 				if(checkR  == false){
	 					ordercons.isDoingSubmit = false;
	 					return;
	 				}else{
	 					ordercons.isCDAndBC = false;
	 					if(orderpopup.checkSupplementRequire(temp,serviceTypeCode)){
	 						orderpopup.addSupplementToRequireArr(serviceTypeCode);
	 						ordercons.isCDAndBC = true;
	 					}
	 				}
	 			}else{
	 				layer.alert(ret);
	 				ordercons.isDoingSubmit = false;
	 				return;
	 			}
	 		}
	 		var isSaveFlag = true;
	 		//诉求列表大于0提交时，如果表单必填项都不为空，提示是否添加至诉求列表
	 		if(rIndex!='-1' && orderfunc.custRequireList.length>0 && !orderpopup.checkRequireFormNull() && contactOrderStatus !='10') {
	 			if(serviceTypeCode=="TS"||serviceTypeCode=="BC"||serviceTypeCode=="CD"||serviceTypeCode=="ML"){
	 				orderfunc.addRequireList();
	 			}else {
	 				isSaveFlag = false;
	 			}
	 		}
	 		if(true){
	 			orderpopup.doSubmit(temp,baseSize,rIndex);
	 		}else{
	 			layer.confirm("你还有诉求信息没添加至诉求列表，是否提交工单？",{icon: 3,shadeClose:false},function(index){
	 				orderpopup.doSubmit(temp,baseSize,rIndex);
	 			},function(index){
	 				layer.close(index);
	 				ordercons.isDoingSubmit = false;
	 				return ;
	 			});
	 		}
	 	},
	 	doSubmit : function(temp,baseSize,rIndex) {
	 		// 异步删除预约回访数据 add by shenz 20181031
	 		orderfunc.delCallBackUser();
	 		var contactOrderVO = orderpopup.getCustFormData(temp);
	 		contactOrderVO.contactOrderStatus ='';
	 		var contactCallRecordVO = orderpopup.getCallRecordVO(temp);;
	 		var contactUserRequireVOList = orderpopup.getRequireList();

	 		if(!(contactUserRequireVOList && contactUserRequireVOList.length>0)) {
	 			layer.alert("接入单诉求为空，无法提交！");
	 			ordercons.isDoingSubmit = false;
	 			return;
	 		}
	 		if(checkNull(contactOrderVO.orgCode)) { //用户信息加入主体
	 			contactOrderVO.orgCode = contactUserRequireVOList[0].orgCode;  
	 		}
	 		var updateOrSubmit = '';
	 	
	 		if(!checkNull(contactOrderVO.contactOrderCode)) { //提交还是更新
	 			updateOrSubmit = "update";
	 			//新增诉求，只需要提交新增部分
	 			if(contactUserRequireVOList.length > baseSize && baseSize>0){
	 				var requireTemp = [];
	 				for(var k = baseSize ;k<contactUserRequireVOList.length ;k++) {
	 					requireTemp.push(contactUserRequireVOList[k]);
	 				}
	 				contactUserRequireVOList = requireTemp;
	 			}
	 		}else{
	 			updateOrSubmit = "submit";
	 		}
	 		if(contactUserRequireVOList!=null){
	 			for(var i in  contactUserRequireVOList){//需要提交的数据校验
		 			if((contactUserRequireVOList[i].contactOrderSerItem1Name=='被动修改结果或服务请求'||contactUserRequireVOList[i].contactOrderSerItem1Code=='ZX08'
	 					||contactUserRequireVOList[i].contactOrderSerItem1Name=='事中或事后报单'||contactUserRequireVOList[i].contactOrderSerItem1Code=='ZX07')
	 					&&orderVue.$data.neworderAFrontAgent){
		 				//如果有事中或事后报单||被动修改结果或服务请求 则需要校验是否二线坐席 如果一线无提交功能
	 				    layer.alert('一线坐席无法提交')
	 			 		ordercons.isDoingSubmit = false;
			 		}
		 			if(contactUserRequireVOList[i].disposeType=="12"&&JSON.stringify(ordercons.ecmOrderObj)=="{}"){//判断提交单是否存在转电商的却没有电商订单信息
			 			layer.alert("无订单信息，请重新从【电商订单查询】中选择订单信息！");
			 			ordercons.isDoingSubmit = false;
			 		}
	 				if(!ordercons.isDoingSubmit){
				    	var temp1=JSON.parse(JSON.stringify(orderVue.$data.formData))
	 					var ret1 = orderpopup.checkRequireForm(temp1);//都没填写temp= JSON.parse(JSON.stringify(orderVue.$data.formData));
	 					if(rIndex == '-1' && baseSize >= 0 &&  orderVue.$data.formData.contactOrderStatus !='10'){//rIndex当前选择诉求，baseSize原始诉求
	 		 				if(ret1 == true){
	 		 					if(ordercons.isCDAndBC == true){  //催单补充需要删除两个诉求
	 		 						orderfunc.custRequireList.splice(orderfunc.custRequireList.length-2,2);
	 		 					}else{
	 		 						orderfunc.custRequireList.splice(orderfunc.custRequireList.length-1,1);
	 		 					}
	 		 				}
	 		 			}
		 		 		return;
				    }
		 		}
	 		}
	 		//渲染表单
	 		var cInfo = orderpopup.getConfirmInfo();
	 		layer.confirm(cInfo.content,{icon: 3, title:cInfo.title,closeBtn:0,shadeClose:false},function(index){
	 			ordercons.preOperate = 'submit';
	 			var param = {};
	 			param.updateOrSubmit = updateOrSubmit;
	 			param.contactOrderVO = contactOrderVO;
	 			param.contactCallRecordVO = contactCallRecordVO;
	 			param.contactUserRequireVOList = contactUserRequireVOList;
	 			param.ecmOrderVO = ordercons.ecmOrderObj;
	 			param.orderCreatePerson = orderCreatePerson;
	 			param.callType = "manual";
	 			
	 			var serialTemp = contactOrderVO.customerPhone.substring(0,7);
	 			console.info("orderSubmit >> request : {"+serialTemp+ "}提交~~~");
	 			ajax.remoteCall("/neworder/servlet/contact?action=submitOrder",param,function(result) {
	 				ordercons.isDoingSubmit = false;
	 				console.log("orderSubmit >> result :{"+serialTemp+ "}返回结果："+JSON.stringify(result));
	 				try {
		 				if(result.state == 1){
		 					console.log("orderSubmit >> result :{"+serialTemp+ "} begin closing window...");
		 					layer.alert(formatNull(result.msg,"界面未正常关闭，请先检查该单据是否已生成，请勿重复提交！"),{icon: 1},function(){
		 						orderpopup.afterSubmit();
		 					});
		 				}else{
		 					console.log("orderSubmit >> result :{"+serialTemp+ "} submit with error...");
		 					var temp1=JSON.parse(JSON.stringify(orderVue.$data.formData))
		 					var ret = orderpopup.checkRequireForm(temp1);//都没填写temp= JSON.parse(JSON.stringify(orderVue.$data.formData));
		 					if(rIndex == '-1' && baseSize >= 0 &&  orderVue.$data.formData.contactOrderStatus !='10'){//rIndex当前选择诉求，baseSize原始诉求
		 		 				if(ret == true){
		 		 					if(ordercons.isCDAndBC == true){  //催单补充需要删除两个诉求
		 		 						orderfunc.custRequireList.splice(orderfunc.custRequireList.length-2,2);
		 		 					}else{
		 		 						orderfunc.custRequireList.splice(orderfunc.custRequireList.length-1,1);
		 		 					}
		 		 				}
		 		 			}
		 					if(result.msg && result.msg == 'localDB-failed'){
		 						layer.alert("保存到本地失败!请截图发送给管理员，并点击确认按钮!",{icon: 5,shadeClose:false},function(){
		 							orderpopup.afterSubmit(topid);
		 						});
		 					}else{
		 						layer.alert(formatNull(result.msg,"界面未正常关闭，请先检查该单据是否已生成，请勿重复提交！"),{icon: 5,shadeClose:false});
		 					}
		 				}
	 				} catch (e) {
	 					console.log("[doSubmit]提交单据后出现异常,异常信息："+e.name+","+e.message+","+e.description);
	 					layer.alert("界面未正常关闭，请先检查该单据是否已生成，请勿重复提交！",{icon: 5,shadeClose:false});
	 				}
	 			});
	 		},function(index){
	 			ordercons.isDoingSubmit = false;
	 			var temp1=JSON.parse(JSON.stringify(orderVue.$data.formData))
	 			var ret = orderpopup.checkRequireForm(temp1);//都没填写temp= JSON.parse(JSON.stringify(orderVue.$data.formData));
	 			if(rIndex == '-1' && baseSize >= 0 &&  orderVue.$data.formData.contactOrderStatus !='10'){//rIndex当前选择诉求，baseSize原始诉求
	 				if(ret == true){
	 					if(ordercons.isCDAndBC == true){  //催单补充需要删除两个诉求
	 						orderfunc.custRequireList.splice(orderfunc.custRequireList.length-2,2);
	 					}else{
	 						orderfunc.custRequireList.splice(orderfunc.custRequireList.length-1,1);
	 					}
	 				}
	 			}
	 			layer.close(index);
			});
	 	},
	 	afterSubmit : function() {
	 		//同时创建小结单
			orderpopup.submit_online(true);
			orderfunc.cleanFormData();
	 	},
	 	saveMap:function(id,mapData){
	 		if(id!=null&&id!=""&&mapData!=""){
	 			try {
		 			ajax.remoteCall("/neworder/servlet/contact?action=saveMap",{id:id,mapdata:mapData},function(result) {}); 
				} catch (e) {
					layer.alert("地图信息提交异常请先检查该单据是否已生成，请勿重复提交！",{icon: 5,shadeClose:false});
				}
	 		}
	 	},
	 	//暂存
	 	save_click : function() {
	 		if(ordercons.isDoingSubmit){
	 			console.log("正在做提交或暂存操作，无法重复执行...");
	 			return;
	 		}
	 		// 异步删除预约回访数据 add by shenz 20181031
	 		orderfunc.delCallBackUser();
	 		
	 		if(checkNum==0&&!orderfunc.checkAllContactorderservicedescribe()){//校验服务描述是否存存在xx
	 			checkNum=1;
	 			return;
	 		}
	 		if(checkNum==0&&!orderfunc.checkContactorderservicedescribe()){//校验服务描是否为xx
	 			checkNum=1;
	 			return;
	 		}        
            var flag = orderVue.$data.formData.isHurry ;//标记判断是否选中一个
            if(flag){
            	layer.alert("电商紧急催单可通过席间信息发“全网客服”，请不要勾选线下催单！",{icon: 5,shadeClose:false});
            	return;
            }
            var requireVOList = orderpopup.getRequireList();//检测已有的诉求列表
	 		for(var i in requireVOList){
	 			if(requireVOList[i]!=null&&requireVOList[i].isHurry!=null&&requireVOList[i].isHurry=='Y'){//线下催单
					layer.alert("电商紧急催单可通过席间信息发“全网客服”，请不要勾选线下催单！",{icon: 5,shadeClose:false});
					orderpopup.returnRequireList();
		 			ordercons.isDoingSubmit = false;
		 			return;
	 			}
	 		}
	 		if(!orderpopup.checkCustForm(1)){ //提交客户资料校验
	 			ordercons.isDoingSubmit = false;
	 			return;
	 		}
	 		//获取vue中数据
	 		var temp = JSON.parse(JSON.stringify(orderVue.$data.formData));
	 		//获取当前诉求集合的索引
	 		var rIndex = orderVue.$data.formData.requireListIndex;
	 		//转全网校验
	 		var disposeType = orderVue.$data.formData.disposeType;
	 		if(rIndex == '-1' && disposeType =='14'){
	 			var checkRF = orderpopup.checkRequireForm(temp); 
	 			if(checkRF != true){
	 				layer.alert(checkRF);
	 				ordercons.isDoingSubmit = false;
 					return;
 				}
	 		}
	 	
	 		//诉求列表存在非诉求列表数据
	 		if(rIndex == '-1' && !orderpopup.checkRequireFormNull('1')){
	 			var checkR = orderpopup.checkRequireFrom(false); //不校验添加诉求集合
	 			if(checkR  == false){
	 				ordercons.isDoingSubmit = false;
 					return;
 				}
	 		}
	 		
	 		//诉求列表大于0提交时，如果表单必填项都不为空，提示是否添加至诉求列表
	 		if(rIndex != '-1' && orderfunc.custRequireList.length>0 && !orderpopup.checkRequireFormNull()) {
	 			orderpopup.checkRequireFrom(false);
	 		}
	 		
	 		ordercons.preOperate = 'save';
	 		var contactOrderVO = orderpopup.getCustFormData(temp);
	 		contactOrderVO.contactOrderStatus ='10';
	 		var contactCallRecordVO = orderpopup.getCallRecordVO(temp);
            var contactUserRequireVOList = orderpopup.getRequireList();//检测已有的诉求列表

	 		var contactDelRequireIdList = orderfunc.deleteCustRequireIdList;//要删除的诉求
	 		var param = {};
	 		if(contactUserRequireVOList){
	 			//对已有的诉求列表进行判断，是否有多条催单记录
	 			var reqList = contactUserRequireVOList
		 		for(var i=0;i<reqList.length-1;i++){
		 			for(var j=i+1;j<reqList.length;j++){
		 				if(reqList[i].prodCode==reqList[j].prodCode&&reqList[i].contactOrderServTypeCode=="CD"&&reqList[j].contactOrderServTypeCode=="CD"&&reqList[i].requireCreateDate==""&&reqList[j].requireCreateDate==""){
		 					layer.alert("相同产品品类只能添加一条催单诉求记录",{icon:5});
		 	 	 			return	
		 				}
		 			}
		 		}
	 		}
	 		param.contactOrderVO = contactOrderVO;
	 		param.contactUserRequireVOList = contactUserRequireVOList;
	 		param.contactDelRequireIdList = contactDelRequireIdList;
	 		param.ecmOrderVO = ordercons.ecmOrderObj;
	 		ajax.remoteCall("/neworder/servlet/contact?action=saveOrder",param,function(result) {
	 			ordercons.isDoingSubmit = false;
	 			if(result.state == 1){
	 				orderpopup.submit_online(true);
	 				layer.alert(result.msg,{icon:1},function(index){
	 					orderfunc.cleanFormData();
	 					layer.close(index);
	 				});
	 			}else{
	 				layer.msg(result.msg,{icon:5});
	 			}
	 		});
	 	},
	 	//voc提交
	 	voc_click : function() {
	 		var sessionId = orderVue.$data.formData.sessionId;
	 		var moduleName = orderVue.$data.formData.otherModuleName;
	 		var contactSerailId = orderVue.$data.formData.contactOrderSerailId;
	 		var contactCode = orderVue.$data.formData.contactOrderCode;
	 		var url = '/PerformExamWeb/pages/voc/perfect2.jsp?';
	 		if(!checkNull(sessionId)){
	 			ajax.remoteCall("/neworder/servlet/contact?query=agentInfo",null,function(result){
	 				var r = result.data;
	 				if(!checkNull(result.data) && !checkNull(sessionId)){
	 					var param = 'orderStatu=1&perID='+sessionId  + '&AGENT_NAME=' + r.agentAcc+'&AGENT_ACC=' + r.agentAcc
					 			  + '&AGENT_DEPT=' + r.agentDept +"&DEPT_NAME="+ r.deptName + orderpopup.getVocNeedData(1);
	 					if(moduleName == '02'){
	 						param = "SESION_TYPE=02&" + param;
	 						url =  "/PerformExamWeb/pages/voc/perfect2-meiti.jsp?";
	 					}
	 					popup.layerShow({type:2,title:'VOC提交',area:['1024px','665px'],offset:'20px',shade:0,maxmin: true},url+param);
	 				}
	 			})
	 		}else if(checkNull(sessionId) && (!checkNull(contactCode) || !checkNull(contactSerailId))){
	 			var data = {};
	 			data.contactCode = contactCode;
	 			data.contactSerailId = contactSerailId;
	 			ajax.remoteCall("/neworder/servlet/contact?action=sessionIdByContactCode",data,function(result){
	 				var r = result.data;
	 				if(!checkNull(result.data) && !checkNull(r.sessionId)){
	 					var param = 'orderStatu=1&perID='+r.sessionId  + '&AGENT_NAME=' + r.agentAcc
					 			  + '&AGENT_DEPT=' + r.agentDept + orderpopup.getVocNeedData(1);
	 					console.log(param);
	 					if(r.moduleName == '02'){
	 						param = "SESION_TYPE=02&" + param;
	 						url =  "/PerformExamWeb/pages/voc/perfect2-meiti.jsp?";
	 					}
	 					popup.layerShow({type:2,title:'VOC提交',area:['1024px','665px'],offset:'20px',shade:0,maxmin: true},url+param);
	 				}else{
	 					layer.alert("该单不存在录音，无法进行VOC提交!");
	 				}
	 			});
	 		}else{
	 			layer.alert("该单不存在录音，无法进行VOC提交!");
	 		}
	 	},
	 	marketing:function(){
	 		if(orderVue.$data.formData.customerName==''){
	 			orderVue.$message("姓名不为空");
	 			return;
	 		}
	 		if(orderVue.$data.formData.customerMobilephone1==''){
	 			orderVue.$message("电话号码1不为空");
	 			return;
	 		}
	 		if(orderVue.$data.formData.areaNum==''){
	 			orderVue.$message("电话区号不为空");
	 			return;
	 		}
	 		if(orderVue.$data.formData.areaCode==''){
	 			orderVue.$message("区域编码不为空");
	 			return;
	 		}
	 		if(orderVue.$data.formData.areaName==''){
	 			orderVue.$message("区域名称不为空");
	 			return;
	 		}
	 		if(orderVue.$data.formData.customerAddress==''){
	 			orderVue.$message("用户住址不为空");
	 			return;
	 		}
	 		if(orderVue.$data.formData.orgCode==''){
	 			orderVue.$message("主体不为空");
	 			return;
	 		}if(orderVue.$data.formData.brandName==''){
	 			orderVue.$message("品牌不为空");
	 			return;
	 		}if(orderVue.$data.formData.prodName==''){
	 			orderVue.$message("品类不为空");
	 			return;
	 		}
	 		var orgName = orderVue.$data.orgCodeOptions[orderVue.$data.formData.orgCode];
	 		var marketingData={}
	 		marketingData.customerName=orderVue.$data.formData.customerName;
	 		marketingData.customerMobilephone1=orderVue.$data.formData.customerMobilephone1;
	 		marketingData.customerMobilephone2=orderVue.$data.formData.customerMobilephone2;
	 		marketingData.customerMobilephone3=orderVue.$data.formData.customerMobilephone3;
	 		marketingData.areaNum=orderVue.$data.formData.areaNum;
	 		marketingData.areaCode=orderVue.$data.formData.areaCode;
	 		marketingData.areaName=orderVue.$data.formData.areaName;
	 		marketingData.customerAddress=orderVue.$data.formData.customerAddress;
	 		marketingData.orgCode=orderVue.$data.formData.orgCode;
	 		marketingData.orgName=orgName;
	 		marketingData.brandName=orderVue.$data.formData.brandName;
	 		marketingData.brandCode=orderVue.$data.formData.brandCode;
	 		marketingData.prodName=orderVue.$data.formData.prodName;
	 		marketingData.prodCode=orderVue.$data.formData.prodCode;
	 		marketingData.productModel=orderVue.$data.formData.productModel;
	 		marketingData.unitName=orderVue.$data.formData.unitName;
	 		marketingData.unitCode=orderVue.$data.formData.unitCode;
	 		marketingData.branchcode=orderVue.$data.formData.branchCode;
	 		marketingData.branchname=orderVue.$data.formData.branchName;
	 		marketingData.customerCode=orderVue.$data.formData.customerCode;//用户档案编码customerCode
		   	window.open("/marketing/servlet/marketing?action=editResource&channelSource=4&mid=&CUST_PHONE="+orderVue.$data.formData.customerMobilephone1 + "&marketingData=" + encodeURIComponent(JSON.stringify(marketingData)));
	 	},
	 	//上传
	 	upload_click:function(){
	 		var busiId = $("#uploadBusiId").val();
	    	if(busiId == "" && busiId == null){
		    	busiId = "";
	    	}
	    	var busiType = "neworder";
	    	var requestType= "upload"
	    	popup.layerShow({
				type : 2,
				title : "附件管理",
				offset : '20px',
				shadeClose:false,
				area : [ '800px', '650px' ]
			}, "/attachment/servlet/attachment?action=attachment", {busiId:busiId,requestType:requestType,busiType:busiType});
	 	},
	 	//获取提交提示
	 	getConfirmInfo : function(type){
	 		// 给出系统提交提示
	 		var content = "您确定提交工单吗？";
	 		var title = '提交接入单';
	 		//当处理方式为“解决”的时候，特别提示一下
	 		var disposeType = orderVue.$data.formData.disposeType;
	 		if ( !checkNull(disposeType) && disposeType == "10") {
	 			content = '您确定提交目前单据为<strong><code>【解决】</code></strong>单据吗？';
	 		}
	 		//当服务请求是事中或事后报单的时候，特别提示一下
	 		for (var i = 0; i < orderfunc.custRequireList.length; i++) {
	 			if (orderfunc.custRequireList[i].contactOrderSerItem2Code == "ZX07001") {
	 				content = '您确定提交服务请求为<strong><code>【事中或事后报单】</code></strong>的工单吗？';
	 			}
	 			if (orderfunc.custRequireList[i].contactOrderSerItem2Code == "ZX08001") {
	 				content = '您确定提交服务请求为<strong><code>【被动修改结果或服务请求】</code></strong>的工单吗？';
	 			}
	 			
	 		}
	 		/*if ($("#contactOrderService_id_3").val() && $("#contactOrderService_id_3").val() != null && $("#contactOrderService_id_3").val() == "ZX07001") {
	 			content = '您确定提交服务请求为<strong><code>【事中或事后报单】</code></strong>的工单吗？';
	 			className = 'col-md-5 col-md-offset-3';
	 		}*/
	 		if (type == 'save') {
	 			content = "您确定暂存工单吗？";
	 			title = '暂存接入单';
	 		}
	 		var info = {};
	 		info.content = content;
	 		info.title = title;
	 		return info;
	 	},
	 	//求助专家
	 	help_click : function() {
	 		var data = {};
	 		data.orgCode = orderVue.$data.formData.orgCode;
	 		if(!checkNull(data.orgCode)){
	 			data.orgName = $("#orgCode").find("option[value='"+data.orgCode+"']").text();
	 		}
	 		data.brandCode = orderVue.$data.formData.brandCode;
	 		data.brandName = orderVue.$data.formData.brandName;
	 		data.prodCode = orderVue.$data.formData.prodCode;
	 		data.prodName = orderVue.$data.formData.prodName;
	 		top.myCCbar.openAsk(data);
	 	},
	 	//获取接入单来电记录数据
	 	getCallRecordVO : function(data) {
	 		var vo = {};
	 		if(data && data.sessionId != '' && data.customerPhone != '') {
	 			vo.contactOrderCcRecordId = data.sessionId;
	 			vo.contactOrderCallTime = new Date().getTime();
	 			vo.contactOrderCallTel = data.customerPhone;
	 		}
	 		return vo;
	 	},
	 	//获取用户信息
	 	getCustFormData : function(data) {
	 		if(data){
	 			orderpopup.deleteCommonItem(data);
	 			//delete data.orgCode;
	 			data.isNotifyBranch = data.isNotifyBranch == true?"Y":"N";
 				data.manualUpgradeFlag = data.manualUpgradeFlag == true?"Y":"N";
	 			delete data.contactRequireSerailId;
	 			delete data.brandCode;
	 			delete data.brandName;
	 			delete data.prodCode;
	 			delete data.prodName;
	 			delete data.productCode;
	 			delete data.productModel;
	 			delete data.productAmount;
	 			delete data.contactOrderProductUse;
	 			delete data.contactOrderServTypeCode;
	 			delete data.contactOrderSerItem1Code;
	 			delete data.contactOrderSerItem1Name;
	 			delete data.contactOrderSerItem2Code;
	 			delete data.contactOrderSerItem2Name;
	 			delete data.serviceMainTypeCode;
	 			delete data.serviceMainTypeName;
	 			delete data.serviceSubTypeCode;
	 			delete data.serviceSubTypeName;
	 			delete data.contactOrderBuyDate;
	 			delete data.contactOrderBuyChannel;
	 			delete data.machineErrorDisplay;
	 			delete data.unitCode;
	 			delete data.unitName;
	 			delete data.appointDate;
	 			delete data.appointTimeDesc;
	 			//delete data.sourceOrderCode;
	 			delete data.urgentLevel;
	 			delete data.disposeType;
	 			delete data.complaintLevel;
	 			delete data.appointSceneCode;
	 			delete data.contactOrderSaleUnitCode;
	 			delete data.contactOrderSaleUnitName;
	 			delete data.contactOrderServiceDescribe;
	 			delete data.pubRemark;
	 			delete data.isNotifyBranch;
	 			delete data.branchCode;
	 			delete data.branchName;
	 			delete data.ecmOrder;
	 			delete data.contactOrderSerItemName;
	 			delete data.contactOrderServTypeName;
	 			delete data.excludedFaultFlag;
	 			delete data.isHurry;
	 			delete data.turnWebDepict;
	 			delete data.contactCallNum;
	 		}
	 		return data;
	 	},
	 	//获取诉求列表数据
	 	getRequireList : function() {
	 		var result =[];
	 		var data = orderfunc.custRequireList;
	 		if(data && data.length>0) {
	 			for(var i=0 ;i < data.length ;i++) {
	 				orderpopup.deleteCommonItem(data[i]);
	 				orderpopup.addRequireProductInfo(data[i]);
	 				data[i].contactOrderServiceDesc = data[i].contactOrderServiceDescribe;
	 				data[i].isNotifyBranch = data[i].isNotifyBranch == true?"Y":"N";
	 				data[i].manualUpgradeFlag = data[i].manualUpgradeFlag == true?"Y":"N";
	 				data[i].isHurry = data[i].isHurry == true?"Y":"N"
		 			delete data[i].contactTypeCode;
		 			delete data[i].areaNum;
		 			delete data[i].customerName;
		 			delete data[i].customerPhone;
		 			delete data[i].customerType;
		 			delete data[i].customerLevel;
		 			delete data[i].customerStarLevel;
		 			delete data[i].serviceCustomerName;
		 			delete data[i].serviceCustomerMobilephone1;
		 			delete data[i].serviceCustomerMobilephone12;
		 			delete data[i].serviceCustomerMobilephone13;
		 			delete data[i].customerMobilephone1;
		 			delete data[i].customerMobilephone2;
		 			delete data[i].customerMobilephone3;
		 			delete data[i].customerAddress;
		 			delete data[i].areaCode;
		 			delete data[i].areaName;
		 			//delete data[i].otherModuleName;
		 			//delete data[i].otherModuleId;
		 			result.push(data[i])
	 			}
	 		}
	 		return result;
	 	},	//恢复诉求列表部分数据
	 	returnRequireList : function() {
	 		var result =[];
	 		var data = orderfunc.custRequireList;
	 		if(data && data.length>0) {
	 			for(var i=0 ;i < data.length ;i++) {
	 				orderpopup.deleteCommonItem(data[i]);
	 				orderpopup.addRequireProductInfo(data[i]);
	 				data[i].contactOrderServiceDesc = data[i].contactOrderServiceDescribe;
	 				data[i].isNotifyBranch = data[i].isNotifyBranch == "Y"?true:false
	 				data[i].manualUpgradeFlag = data[i].manualUpgradeFlag == "Y"?true:false
	 				data[i].isHurry = data[i].isHurry == "Y"?true:false
		 			result.push(data[i])
	 			}
	 		}
	 		return result;
	 	},
	 	//在诉求对象添加产品信息
	 	addRequireProductInfo : function(data) {
	 		var vo = {};
	 		vo.areaCode = data.areaCode;
	 		vo.areaName = data.areaName;
	 		vo.brandCode = data.brandCode;
	 		vo.brandName = data.brandName;
	 		vo.contactOrderBuyChannel = data.contactOrderBuyChannel;
	 		vo.contactOrderCode = data.contactOrderCode;
	 		vo.contactOrderId = data.contactOrderId;
	 		vo.contactOrderBuyDate = data.contactOrderBuyDate;
	 		vo.contactOrderProductUse = data.contactOrderProductUse;
	 		vo.contactOrderSaleUnitCode = data.contactOrderSaleUnitCode;
	 		vo.contactOrderSaleUnitName = data.contactOrderSaleUnitName;
	 		vo.machineAddress = '广州天河';
	 		vo.orgCode = data.orgCode;
	 		vo.prodCode = data.prodCode;
	 		vo.prodName = data.prodName;
	 		vo.productAmount = data.productAmount;
	 		vo.productCode = data.productCode;
	 		vo.productDesc = data.productDesc;
	 		vo.productModel = data.productModel;
	 		//vo.remark = data.obj;
	 		data.contactProductInfoVO = vo;
	 	},
	 	//去除公共元素
	 	deleteCommonItem : function(data) {
	 		if(data){
	 			delete data.isNotifyBranchDisabled;
	 			delete data.customerLevelDisabled;
	 			delete data.custDetailBtn;
	 			delete data.addRequireBtn;
	 			delete data.custFormDisable;
	 			delete data.requireFormDisable;
	 			delete data.copyBtn;
	 			delete data.saveBtn;
	 			delete data.vocBtn;
	 			delete data.helpBtn;
	 			delete data.applyBtn;
	 			delete data.adviceBtn;
	 			delete data.submitBtn;
	 			delete data.requireListIndex;
	 			delete data.isHurryDisabled;
	 			delete data.oldCustomerAddress;
	 			delete data.manualUpgradeFlagDisabled;
	 			delete data.oldMachineErrorDisplay;
	 			delete data.machineErrorDisplayList;
	 			delete data.oldCustomerAddress;
	 			delete data.thinkCustomerAddressList;
	 			delete data.oldProductModel;
	 			delete data.productModelDisplayList;
	 		}
	 	},
	 	//提交用户校验 type 暂存时不校验服务联系人，服务联系电话
	 	checkCustForm : function(type) {
	 		if(checkNull(orderVue.$data.formData.customerName)){
	 			orderVue.$message.error("用户姓名不可为空！");
	 			return false;
	 		}
	 		if(checkNull(orderVue.$data.formData.areaNum)){
	 			orderVue.$message.error("电话区号不能为空！");
	 			return false;
	 		}
	 		if(checkNull(orderVue.$data.formData.customerMobilephone1)){
	 			orderVue.$message.error("用户号码1不能为空！");
	 			return false;
	 		}
	 		if(checkNull(orderVue.$data.formData.areaName)){
	 			orderVue.$message.error("区域名称不能为空！");
	 			return false;
	 		}
	 		if(checkNull(orderVue.$data.formData.customerAddress)){
	 			orderVue.$message.error("用户地址不能为空！");
	 			return false;
	 		}
	 		if(checkNull(orderVue.$data.formData.contactTypeCode)){
	 			orderVue.$message.error("接入方式不能为空！");
	 			return false;
	 		}
	 		return true;
	 	},
	 	//校验用户诉求
	 	checkRequireForm : function(data) {
 			if(checkNull(data.orgCode)){
 				return "主体编码不能为空！";
 			}
 			if(checkNull(data.prodCode)){
 				return "产品品类不能为空！";
 			}
 			if(checkNull(data.brandCode)){
 				return "产品品牌不能为空！";
 			}
 			var productAmount = data.productAmount;
 			if(checkNull(productAmount)){
 				return "产品数量不能为空！";
 			}else if(productAmount <= 0){
 				return "产品数量必须为正整数,请重新填写！";
 			}
 			if(checkNull(data.contactOrderProductUse)){
 				return "产品用途不能为空！";
 			}
 			if(checkNull(data.contactOrderServTypeCode)){
 				return "服务请求类别不能为空！";
 			}
 			if(checkNull(data.contactOrderSerItem1Code)){
 				return "服务请求项目1不能为空！";
 			}
 			if(checkNull(data.contactOrderSerItem2Code)){
 				return "服务请求项目2不能为空！";
 			}
 			if(checkNull(data.contactOrderServiceDescribe)){
 				return "服务描述不能为空！";
 			}
 			if(data.contactOrderSerItem2Code == "BX01010"){
 				if(checkNull(data.machineErrorDisplay)){
 					return "故障代码不能为空！";
 				}
 			}
 			//购买时间
 			if(!checkNull(data.contactOrderBuyDate) && data.contactOrderBuyDate.length != 10){
 				return "请规范填写“购买日期”，如:2018-05-20,一个零也不能少哦！";
 			} 
 			
 			//校验：填写预约时间段和预约日期必须同时为空或者同时不为空
 			if(checkNull(data.appointDate)!=checkNull(data.appointTimeDesc)){
 				return "请将预约日期和预约时间段信息补充完整！";
 			} 
 			
 			var appointDate = data.appointDate;
 			var appointTimeDescCode = data.appointTimeDesc;
 			if(!checkNull(appointDate)&&!checkNull(appointTimeDescCode)){  //预约时间或者预约时间段不为空
 				var appointTimeDesc = orderVue.$data.appointTimeDescOptions[appointTimeDescCode];
 				var appointTimeDescArr = appointTimeDesc.split("-");
 				var appointDateEnd = appointDate+" "+(appointTimeDescArr[1].toString().length==4?"0"+appointTimeDescArr[1]:appointTimeDescArr[1]);
 				if(new Date(appointDateEnd.replace(/-/g,'/')).getTime()<=new Date().getTime()){
 					return "预约时间必须大于当前时间！";
 				}
 			}
 			var disposeType = data.disposeType;
 			if(checkNull(disposeType)){
 				return "处理方式不能为空！";
 			}
 			
 			//处理方式为转电商的情况下，需要查询并选择电商订单号
 			if(disposeType=='12' && data.sourceOrderCode == ''){
 				return '请从电商订单查询并选择订单号';
 			}
 			
 			//获取接入单状态
 			var contactOrderStatus = data.contactOrderStatus;
 			var serviceTypeCode = data.contactOrderServTypeCode;
 			if((contactOrderStatus==""||contactOrderStatus=="10")&&(serviceTypeCode=="CD"||serviceTypeCode=="BC"||serviceTypeCode=="ML")){
 				return "首次报单不允许提交催单、补充、命令类的诉求信息，请重新选择！";
 			}
 			//校验：如产品型号填写内容为空时，清空产品型号编码的值
 			if(checkNull(data.productModel)){
 				orderfunc.setSingleVueValue('order','productCode','');
 			}
 			//校验：填写了产品型号须匹配出对应的产品编码
 			if(checkNull(data.productModel)!=checkNull(data.productCode)){
 				return "请选择完整的产品型号！";
 			} 
 			//校验：转全网描述
 			if(disposeType=='14' && orderVue.$data.neworderOnlineAgent =="true" && checkNull(data.turnWebDepict)){
 				return "请填写全网处理结果！";
 			} 
// 			if (!orderfunc.checkServiceRequire()){
//	 			return '原始单据服务请求非法，请重新填写！';
//			}
 			return true;
	 	},
	 	//复制单据校验
	 	copy_checkForm : function() {
	 		if(checkNull(orderVue.$data.formData.contactOrderProductUse)){
	 			return "未选取产品用途，无法复制！";
			}
	 		if(orderfunc.custRequireList && orderfunc.custRequireList.length>1){
	 			return "多诉求，无法进行复制！";
			}
			// 校验原单的服务请求和品类是否匹配（防止复制了历史数据，或者送装等不能在CC接入的服务请求）
//			if (!orderfunc.checkServiceRequire()){
//				return "原始单据服务请求非法，无法进行复制！";
//			}
	 		return true;
	 	},
	 	//用户诉求必填项是否全部都填,type = false时进一步校验
	 	checkRequireFormNull : function(type) {
	 		var flag = true;
	 		var data = JSON.parse(JSON.stringify(orderVue.$data.formData));
	 		//必填项有一项不填写即返回false
	 		if(!checkNull(data.orgCode) || !checkNull(data.brandCode) || !checkNull(data.prodCode)
	 			|| !(checkNull(data.contactOrderProductUse) || data.contactOrderProductUse == '10')
	 			|| !checkNull(data.contactOrderSerItem1Code)
	 			|| !checkNull(data.contactOrderSerItem2Code) || !checkNull(data.contactOrderServTypeCode)
	 			|| !checkNull(data.disposeType) || !checkNull(data.contactOrderServiceDescribe)
	 			|| (orderVue.$data.neworderOnlineAgent =="true" && !checkNull(data.turnWebDepict))){
	 			flag = false;
	 		}
	 		//校验产品数量
	 		if(!type) {
	 			if(!checkNull(data.productAmount) && data.productAmount != '1' ) {
	 				flag = false;
	 			}
	 		}
	 		return flag;
	 	},
	 	//一个诉求，且没有添加到诉求列表,check是否校验
	 	checkRequireFrom : function(check) {
	 		var rIndex = orderVue.$data.formData.requireListIndex ;
	 		if( rIndex == '-1' && orderfunc.custRequireList.length >= 0 ) {
	 			if(check){
	 		 		var temp = JSON.parse(JSON.stringify(orderVue.$data.formData));
	 		 		var ret = orderpopup.checkRequireForm(temp);
	 				if(ret != true){
	 					layer.alert(ret);
	 		 			return false;
	 				}
	 		 		orderfunc.readRequireForm();
	 			}else{
	 				orderfunc.readRequireForm();
	 			}
	 		}else if(rIndex != '-1' && orderVue.$data.formData.contactOrderStatus == '10') {
	 			orderfunc.readRequireForm(rIndex-1);
	 		}
	 	},
	 	//获取补充诉求（对报装、报修）进行催单后修改客户信息,data 未vue对象,服务请求一级
	 	checkSupplementRequire : function(data,type) {
	 		if(type == 'CD'){
	 			var base = ['areaNum','areaCode','areaName','customerName'
	 			            ,'customerMobilephone1','customerMobilephone2','customerMobilephone3'
		 		            ,'serviceCustomerMobilephone1','serviceCustomerMobilephone2','serviceCustomerMobilephone3'
		 		            ,'customerAddress','serviceCustomerName'];
		 		for(var i = 0; i < base.length; i++) {
		 			var item = base[i];
		 			if(ordercons.baseRequireObj[item] != data[item]){
		 				return true;
		 			}
		 		}
		 		return false;
	 		}
	 		return false
	 	},
	 	//添加补充诉求到诉求对象
	 	addSupplementToRequireArr : function(type) {
	 		var index = orderfunc.custRequireList.length;
	 		if(type == 'CD' && ordercons.baseRequireSize > 0 && index > 0 ){
	 			var data = JSON.parse(JSON.stringify(orderfunc.custRequireList[index - 1]));
	 			if(!checkNull(data.contactUserRequireId)){
	 				data.contactOrderServTypeCode = "BC";
	 				data.contactOrderServTypeName = "补充";
	 				data.contactOrderSerItem1Code = "BC01";
	 				data.contactOrderSerItem1Name = "补充";
	 				data.contactOrderSerItem2Code = "BC01002";
	 				data.contactOrderSerItem2Name = "补充用户信息";
	 				data.contactOrderSerItemName = "补充、补充用户信息";
	 				data.serviceMainTypeCode = "0";
	 				data.serviceMainTypeName = "（保留原单业务类型）";
	 				data.serviceSubTypeCode = "0";
	 				data.serviceSubTypeName = "（保留原单业务类型）";
	 				data.contactOrderServiceDescribe = "[自动]  补充用户信息";
	 				orderfunc.custRequireList.push(data);
	 			}
	 		}
	 	},
	 	//获取VOC数据(1)和建议数据(2)
	 	getVocNeedData : function(type) {
	 		var vo = JSON.parse(JSON.stringify(orderVue.$data.formData));
	 		var param = null;
	 		if(type == 1 || type == 2 ){
	 			param = '&VOC_APPLY_TYPE=01&CUSTOMER_MOBILEPHONE=' + vo.customerMobilephone1
	 			+ '&CUSTOMER_MOBILEPHONE2=' +  vo.customerMobilephone2
	 			+ '&CUSTOMER_MOBILEPHONE3=' +  vo.customerMobilephone3
	 			+ '&CUSTOMER_NAME=' +  vo.customerName
	 			+ '&CUSTOMER_ADDRESS=' +  vo.customerAddress
	 			+ '&ORG_CODE=' + vo.orgCode
	 			+ '&BRAND_CODE=' + vo.brandCode
	 			+ '&BRAND_CODE_NAME=' + vo.brandName
	 			+ '&PROD_CODE=' + vo.prodCode
	 			+ '&PROD_CODE_NAME=' + vo.prodName
	 			+ '&CONTACT_ORDER_SERV_TYPE_CODE=' + vo.contactOrderServTypeCode
	 			+ '&BUY_TIME=' + vo.contactOrderBuyDate
	 			+ '&BUY_UNIT=' + vo.contactOrderSaleUnitName
	 			+ '&PRODUCT_MODEL=' + vo.productModel
	 			+ '&PRODUCT_CODE=' + vo.productCode
	 			+ '&AREA_NUM=' + vo.areaNum
	 			+ '&wecomFlag=true' 
	 			+ '&REQ_CONTENT=' + vo.contactOrderServiceDescribe
	 			+ '&CONTACT_ORDER_SERV_TYPE2_NAME=' + vo.contactOrderSerItem2Name;
	 		}else{
	 			param = "?p=ORIGIN_ORDER_NO*101*"+ vo.contactOrderCode
				+"*010*UNITCODE*101*"+ vo.unitCode
				+"*010*UNITNAME*101*"+ vo.unitName
				+"*010*BRANCH_CODE*101*"+ vo.branchCode
				+"*010*BRANCH_NAME*101*"+ vo.branchName
				+"*010*SETTLEMENTUNITNAME*101*"+ vo.contactOrderSaleUnitName
				+"*010*CUST_NAME*101*"+ vo.customerName
				+"*010*CUSTOMER_TEL*101*"+ vo.customerMobilephone1
				+"*010*CUSTOMER_STAR_LEVEL*101*"+ vo.customerStarLevel
				+"*010*CUSTOMER_LEVEL*101*"+ vo.customerLevel
				+"*010*CUSTOMER_ADDRESS*101*"+ vo.customerAddress
				+"*010*REQUIRE_DESC*101*"+ vo.contactOrderServiceDescribe
				+"*010*PROD_CODE*101*"+ vo.prodCode
				+"*010*PROD_NAME*101*"+ vo.prodName
				+"*010*ORG_CODE*101*"+ vo.orgCode
				+"*010*serviceMainTypeCode*101*"+vo.serviceMainTypeCode
				+"*010*serviceMainTypeName*101*"+vo.serviceMainTypeName
				+"*010*serviceSubTypeCode*101*"+vo.serviceMainTypeCode
				+"*010*serviceSubTypeName*101*"+vo.serviceMainTypeName
				+"*010*BRAND_CODE*101*"+ vo.brandCode
				+"*010*BRAND_NAME*101*"+ vo.brandName
				+"*010*PRODUCT_MODEL*101*"+ vo.productModel
				+"*010*serviceOrderId*101*"+vo.contactOrderCode
				+"*010*serviceCustomerDemandId*101*"+vo.contactUserRequireId
				+"*010*SESSION_ID*101*"+ orderVue.$data.formData.sessionId
	 		}
 			param = encodeURI(param);
	 		return param;
	 	},
	 	//获取统一口径内容的html
		getUnifyExplainHtml : function(arr){
			var _prodCode = [];
			var text = "<div style='padding:10px;color:black;height:100%;overflow:auto;font-size:16px;'>";
			for(var i in arr) {
				var data = arr[i];
				if($.inArray(data.prodCode , _prodCode) >= 0 ) {
					continue;
				}
				text += "<fieldset style='padding:.35em .625em .75em;margin:0 2px 5px;border:1px solid silver'>" 
					 + "<legend style='padding:0;border:0;width:auto;margin-bottom:5px;font-size:15px;color:#26a0bd;'>"
					 + "[ "+timestamp2Str(data.pubCreateDate,'date')+" ][ "+data.brandName+" "+data.prodName+" ]</legend>"
					 + "<p>"+data.unifyExplainInfo+"</p>"
					 + "</fieldset>";
				_prodCode.push(data.prodCode);
			}
			return text + "</div>"
		},
	 	//刷新接入单查询处理界面列表
		refreshParentList : function(){
			var frames = $(top.document.getElementsByClassName('pages-group-tabs-content')).find('iframe');
		    for(var i=0; i<frames.length; i++){
		    	var item = frames[i];
		    	if(/order-list/.test(item.src)){
		    		return item;
		    	}
		    }
		    return false;
		},
	 	getWindowStyle : function() {
	 		var nLeft = ( window.screen.width - 1200 )/2;
			var nTop = (window.screen.height- 700 )/2;
			var style="width=1200px,height=700px,left="+nLeft+"px,top="+nTop+"px,scrollbars=yes,resizable=no,status=1";
			return style;
	 	},

	 	productMarketing : function(){
	 		var phone = orderVue.$data.formData.customerMobilephone1;
	 		var prodCode = orderVue.$data.formData.prodCode;
	 		if(phone==""){
	 			orderVue.$message("电话号码1不为空",{icon:5});
	 			return;
	 		}
		   	window.open("/marketing/pages/productMarketing/product_marketing.jsp?phone="+phone+"&prodCode="+prodCode);	 
	 	},
	 	marketButton : function(){
	 		var html=marketButtonTemplate.render('');
	 		layer.tips(html, '#marketButton', {
			  tips: [2, '#fff'],
			  time: 5000
			});	 
		},


}