<!DOCTYPE html>
<html>

<head>
  <title>敏感词触发记录</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
  <!-- 基础的 css js 资源 -->
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css" />
  <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.1" />
  <style>
  </style>
</head>

<body class="yq-page-full vue-box">
  <div id="sensitiveRecord" class="flex yq-table-page" v-loading="tableData.loading"
    element-loading-text="加载中..." v-cloak>
    <div class="yq-card">
      <div class="card-header">
        <div class="head-title">
          敏感词触发记录
        </div>
      </div>
      <div class="card-content">
        <senior-search :show.sync="show">
          <el-form class="search-form" :inline="false" :model="searchForm" ref="searchForm" size="small"
            label-width="120px">

            <el-form-item label="触发消息时间">
              <el-date-picker v-model="searchForm.date" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始时间" end-placeholder="结束时间" :default-time="['00:00:00', '23:59:59']"
                :picker-options="pickerOptions" :unlink-panels="true" :clearable="false" style="width: 280px;">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="是否为坐席">
              <el-select v-model="searchForm.isAgent" placeholder="请选择" clearable style="width: 120px;">
                <el-option label="是" value="1"></el-option>
                <el-option label="否" value="0"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item class="btns" label-width="0px">
              <el-button type="primary" plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
              <el-button type="primary" icon="el-icon-search" @click="getList(1)">查询</el-button>
            </el-form-item>
          </el-form>
        </senior-search>

        <div class="yq-table">
          <el-table stripe :data="tableData.data" height="100%" fit ref="table" style="flex: 1;overflow: auto;" border>
            <el-table-column prop="KEY_WORD" label="触发敏感词" min-width="120"></el-table-column>
            <el-table-column prop="TRIGGER_TIME" label="触发消息时间" min-width="150" sortable></el-table-column>
            <el-table-column prop="WX_ACCOUNT_NAME" label="触发企微账号名称" min-width="150"></el-table-column>
            <el-table-column prop="WX_ACCOUNT_ID" label="触发企微账号ID" min-width="150"></el-table-column>
            <el-table-column prop="AGENT_ACC" label="坐席账号" min-width="120"></el-table-column>
            <el-table-column prop="AGENT_NAME" label="坐席名称" min-width="120"></el-table-column>
            <el-table-column prop="ROOM_NAME" label="群聊名称" min-width="150"></el-table-column>
            <el-table-column prop="IS_AGENT" label="是否为坐席" min-width="100">
              <template slot-scope="scope">
                {{ scope.row.IS_AGENT === '1' ? '是' : '否' }}
              </template>
            </el-table-column>
<!--            <el-table-column prop="IS_TRIGGER_SENSITIVE" label="是否发送" min-width="80">-->
<!--              <template slot-scope="scope">-->
<!--                {{ scope.row.IS_TRIGGER_SENSITIVE === '1' ? '是' : '否' }}-->
<!--              </template>-->
<!--            </el-table-column>-->
          </el-table>

          <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
            :current-page="tableData.pageIndex" :page-size="tableData.pageSize" :page-sizes="[10, 20, 30, 50, 100]"
            layout="total, prev, pager, next, jumper, sizes" :total="tableData.totalRow">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/cc-base/static/js/my_i18n.js?v=202111"></script>
<script src="/cc-base/static/js/i18n.js?v=1"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script src="/wecom/static/js/time.js"></script>
<script>
  // 获取URL参数函数
  function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURIComponent(r[2]);
    return null;
  }

  var appPage = new Vue({
    el: "#sensitiveRecord",
    watch: {
      "searchForm.date": {
        handler: function (newVal, oldVal) {
          if (newVal && newVal.length) {
            this.searchForm.startTime = newVal[0];
            this.searchForm.endTime = newVal[1];
          } else {
            this.searchForm.startTime = "";
            this.searchForm.endTime = "";
          }
        },
        immediate: true,
        deep: true
      }
    },
    data: function () {
      return {
        show: false,
        pickerOptions: {
          shortcuts: [
            {
              text: "今天",
              onClick(picker) {
                const end = getTodayEndTime();
                const start = getTodayStartTime();
                picker.$emit("pick", [start, end]);
              },
            },
            {
              text: "昨天",
              onClick(picker) {
                const end = getYesterDayEndTime();
                const start = getYesterDayStartTime();
                picker.$emit("pick", [start, end]);
              },
            },
            // {
            //   text: "近一周",
            //   onClick(picker) {
            //     const end = getTodayEndTime();
            //     const start = getRecentlySevenDayStartTime();
            //     picker.$emit("pick", [start, end]);
            //   },
            // },
            {
              text: "近一个月",
              onClick(picker) {
                const end = getTodayEndTime();
                const start = getRecentlyOneMonthStartTime();
                picker.$emit("pick", [start, end]);
              },
            },
            {
              text: "近三个月",
              onClick(picker) {
                const end = getTodayEndTime();
                const start = getRecentlyThreeMonthStartTime();
                picker.$emit("pick", [start, end]);
              },
            },
          ],
        },
        searchForm: {
          startTime: getTodayStartTime(),
          endTime: getTodayEndTime(),
          date: [getTodayStartTime(), getTodayEndTime()],
          roomId: getUrlParam('roomId') || '',
          msgTime: getUrlParam('msgTime') || '',
          msgId: getUrlParam('msgId') || '',
          isAgent: ''
        },
        tableData: {
          pageIndex: 1,
          pageSize: 10,
          totalRow: 0,
          data: [],
          loading: false,
        }
      };
    },
    methods: {
      getList: function (page) {
        this.tableData.loading = true;
        if (page) {
          this.tableData.pageIndex = page;
        }

        // 构建请求参数
        const params = {
          data: {
            ...this.searchForm
          },
          pageSize: this.tableData.pageSize,
          pageIndex: this.tableData.pageIndex,
          pageType: '3'
        };

        yq.tableCall('/wecom/webcall?action=sensitive.getList', params, res => {
          this.tableData.loading = false;
          this.tableData.data = res.data || [];
          // this.tableData.pageIndex = res.pageNo;
          this.tableData.totalRow = res.totalRow || 0;

        });
      },
      handleReset: function () {
        // 保留URL传递的参数
        const roomId = this.searchForm.roomId;
        const msgTime = this.searchForm.msgTime;
        const msgId = this.searchForm.msgId;

        this.searchForm = {
          startTime: getTodayStartTime(),
          endTime: getTodayEndTime(),
          date: [getTodayStartTime(), getTodayEndTime()],
          roomId: roomId,
          msgTime: msgTime,
          msgId: msgId,
          isAgent: ''
        };
      },
      onPageChange: function (page) {
        this.tableData.pageIndex = page;
        this.getList();
      },
      onPageSizeChange: function (size) {
        this.tableData.pageSize = size;
        this.getList(1);
      }
    },
    mounted: function () {
      this.getList(1);
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
      window.addEventListener("resize", () => {
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      });
    }
  });
</script>

</html>