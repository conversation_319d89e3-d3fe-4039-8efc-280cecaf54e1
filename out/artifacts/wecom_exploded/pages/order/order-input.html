<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="renderer" content="webkit"> 
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
	<meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible">
	<meta content="yes" name="apple-mobile-web-app-capable">
	<meta content="black" name="apple-mobile-web-app-status-bar-style">
	<meta name="robots" content="index,follow">
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">   
	<link href="/wecom/static/css/element-ui.css" rel="stylesheet">
	<link href="/wecom/static/css/scroll-bar.css" rel="stylesheet">
	<link href="/wecom/static/css/chat.css" rel="stylesheet">
	<style>
		.el-input--small .el-input__inner{
			height: 33px!important
		}
		.el-divider--horizontal{
			margin:10px 0!important
		}
		.el-collapse{
			border-top: 0;
			border-bottom: 0;
		}
		.helpBtnDiv{
			
		}
		.helpBtnDiv button{
			width: 110px;
			margin-bottom: 10px;
			margin-left: 20px!important;
		}
		.el-form-item__content{
			min-height: 29px;
		}
		.el-collapse-item__content{
			padding-bottom:0px;
		}
		.el-collapse-item__header{
			height: 32px;
			font-size: 11px;
		}
		.el-form-item__label{
			font-size: 10px;
		}
		.el-switch{
			width: 150px;
		}
		.el-switch__label.is-active{
			width: 70px;
		}
		.color-red .el-form-item__label{
			color: red;
		}
		.color-blue .el-form-item__label{
			color: blue;
		}
		.pTips{
			margin: 5px 15px 5px 15px;
    		font-size: 12px;
    		color: #eb2b1d;
    		font-weight: bold;
		}
		[v-cloak] {
			display: none;
		}
	</style>
</head>
<body>
	<div id="app">
		<div v-cloak>
			<el-collapse v-model="activeNames2">
				<el-collapse-item title="辅助功能" name="1">
					<div class="helpBtnDiv">
						<el-button type="primary" size="mini" @click="orderpopup.contactOrderSearch()" plain>接入单查询</el-button><br>
						<el-button type="primary" size="mini" @click="orderpopup.salesCenter()" plain>销售中心查询</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.queryStore()" plain>旗舰店查询</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.queryWebsite()" plain>网点查询</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.servicePolicyTab()" plain>备件价格查询</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.prolongservarchives()" plain>延保档案查询</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.chargedetails()" plain>收费明细查询</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.insideBarcode()" plain>产品条码查询</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.marketing()" plain>洗悦家营销</el-button>
						<el-button type="primary" size="mini" @click="orderpopup.productMarketing()" plain>产品营销</el-button>
					</div>
				</el-collapse-item>
			</el-collapse>
		    <el-form ref="formData" :model="formData" :rules="rules" size="mini" label-width="82px" :hide-required-asterisk='true'>
		      <el-collapse v-model="activeNames" @change="">
		      <el-collapse-item title="备忘录" name="0">
		      	<el-row>
			      	<el-col :span="24" :xs="24" style="margin-bottom:15px;">
			            <el-input v-model="note" 
			            	type="textarea" 
			            	:disabled="!formData.custNickname"
			            	@keyup.native="updateNote"
			            	:autosize="{minRows: 2, maxRows: 4}" 
			            	:style="{width: '100%'}">
			            </el-input>
			        </el-col>
		        </el-row>
		      </el-collapse-item>
		      <el-collapse-item title="诉求信息" name="1">
		      	  <div class="pTips" v-html="prodAndBrandTips"></div>
			      <el-row>
			        <el-col :span="6" :xs="12">
			          <el-form-item label="昵称" prop="custNickname">
			            <el-input v-model="formData.custNickname"></el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-red" label="联系电话" prop="customerMobilephone1">
			            <el-input v-model="formData.customerMobilephone1" id="customerMobilephone1" @blur="checkCustNum1" :maxlength="20" 
			              :style="{width: '100%'}"></el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-blue" label="产品主体"  prop="orgCode">
			            <el-select v-model="formData.orgCode" id="orgCode" disabled :style="{width: '100%'}">
			              <el-option v-for="(item, index) in orgCodeOptions" :key="index" :label="item"
			                :value="index"></el-option>
			            </el-select>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-blue" label="产品品牌" prop="brandName">
			            <el-input v-model="formData.brandName" id="brandName" disabled :style="{width: '100%'}">
			            </el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-blue" label="产品品类" prop="prodName">
			            <el-input v-model="formData.prodName" id="prodName" @keyup.enter.native="openProductCodeDiv()" :style="{width: '100%'}">
			            	<el-button slot="append" icon="el-icon-search" @click="openProductCodeDiv()"></el-button> 
			            </el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item label="产品型号" prop="productModel">
			            <el-input v-model="formData.productModel" id="productModel" @keyup.enter.native="openProductModelDiv()" :style="{width: '100%'}">
			            	<el-button slot="append" icon="el-icon-search" @click="openProductModelDiv()"></el-button>
			            </el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="16">
			          <el-form-item label="故障代码" prop="machineErrorDisplay">
			            <el-input v-model="formData.machineErrorDisplay" id="machineErrorDisplay" :style="{width: '100%'}"></el-input>
			          </el-form-item>
			        </el-col>
			      </el-row>
			      <el-row :gutter="10">
			        <el-col :span="12" :xs="12">
			          <el-form-item class="color-red" label="服务请求" prop="contactOrderServTypeCode">
			          	<el-select v-model="formData.contactOrderServTypeCode" id="contactOrderServTypeCode" clearable :style="{width: '100%'}" @change="orderfunc.serviceRequireChange()">
			            	<el-option v-for="(item, index) in contactOrderServTypeCodeOptions" :key="index" :label="item"
			                :value="index" :disabled="item.disabled"></el-option>
			            </el-select>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="4">
			        	<el-input v-model="formData.contactOrderSerItem1Name" id="contactOrderSerItem1Name" disabled size="mini" :style="{width: '100%'}"></el-input>
			        </el-col>
			        <el-col :span="6" :xs="8">
			        	<el-input v-model="formData.contactOrderSerItem2Name" id="contactOrderSerItem2Name" size="mini" @keyup.enter.native="openServerDiv(1)" :style="{width: '100%'}">
			            	<el-button slot="append" icon="el-icon-search" @click="openServerDiv(1)"></el-button> 
			            </el-input>
			        </el-col>
		        </el-row>
		        <el-row>
		        	<el-col :span="12" :xs="24">
			          <el-form-item class="color-blue" label="服务描述" prop="contactOrderServiceDescribe">
			            <el-input v-model="formData.contactOrderServiceDescribe" id="contactOrderServiceDescribe" type="textarea"
			              :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="9" :xs="24">
			          <el-form-item class="color-blue" label="咨询类别" prop="consultType">
			            <el-cascader v-model="formData.consultType" id="consultType" :options="consultTypeOptions"
			              :props="consultTypeProps" :style="{width: '100%'}" separator=">" ref="cascader" @change="consultTypeChange"
			              clearable></el-cascader>
			          </el-form-item>
			        </el-col>
			        <el-col :span="8" :xs="12">
			          <el-form-item class="color-red" label="处理方式" prop="disposeType">
			            <el-select v-model="formData.disposeType" id="disposeType" clearable :style="{width: '100%'}">
			            	<el-option v-for="(item, index) in disposeTypeOptions" :key="index" :label="item"
			                :value="index" :disabled="item.disabled"></el-option>
			            </el-select>
			          </el-form-item>
			        </el-col>
			        <el-col :span="24" :xs="12">
				        <el-form-item class="color-red" label="接入方式" prop="contactTypeCode">
				          <el-select v-model="formData.contactTypeCode" id="contactTypeCode" clearable :style="{width: '100%'}">
			            	<el-option v-for="(item, index) in contactTypeCodeOptions" :key="index" :label="item"
			                :value="index" :disabled="item.disabled"></el-option>
			              </el-select>
				        </el-form-item>
			        </el-col>
		        </el-row>
		        
		      </el-collapse-item>
		      <el-collapse-item title="用户信息" name="2">
		      	  <el-row>
		      	  	<el-col :span="6" :xs="12">
			          <el-form-item label="姓名" prop="customerName" class="color-blue">
			            <el-autocomplete
			            	:style="{width: '100%'}" 
			            	v-model="formData.customerName" 
			            	:fetch-suggestions="querySearch"
			            	@blur="orderfunc.checkSurname()"
			            	:trigger-on-focus="false"
			            	id="customerName">
			            	<el-button slot="append" icon="el-icon-search" @click="openSurnameDiv()"></el-button>
			            </el-autocomplete>
			          </el-form-item>
			        </el-col>
		      	  </el-row>
			      <el-row>
			        <el-col :span="6" :xs="12">
			          <el-form-item label="联系电话2" prop="customerMobilephone2">
			            <el-input v-model="formData.customerMobilephone2" id="customerMobilephone2" @blur="checkNum" :maxlength="20" clearable :style="{width: '100%'}">
			            </el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item label="联系电话3" prop="customerMobilephone3">
			            <el-input v-model="formData.customerMobilephone3" id="customerMobilephone3" @blur="checkNum" :maxlength="20" clearable :style="{width: '100%'}">
			            </el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-red" label="电话区号" prop="areaNum">
			            <el-input v-model="formData.areaNum" @keyup.enter.native="openPhoneCodeDiv('areaNum')" :style="{width: '100%'}">
			              <el-button slot="append" icon="el-icon-search" @click="openPhoneCodeDiv('areaNum')"></el-button>
			          	</el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-red" label="区域" prop="areaName">
			            <el-input v-model="formData.areaName" id="areaName"  @keyup.enter.native="openPhoneCodeDiv('areaName')" :style="{width: '100%'}">
			            </el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="12" :xs="24">
			          <el-form-item class="color-red" label="用户住址" prop="customerAddress">
			            <el-input v-model="formData.customerAddress" id="customerAddress" :style="{width: '100%'}" @blur="checkAddress"></el-input>
			          </el-form-item>
			        </el-col>
			      </el-row>
			      <el-row>
			        <el-col :span="8" :xs="12">
			          <el-form-item label="预约场景" prop="appointSceneCode">
			            <el-select v-model="formData.appointSceneCode" id="appointSceneCode" clearable :style="{width: '100%'}">
			              <el-option v-for="(item, index) in appointSceneCodeOptions" :key="index" :label="item"
			                :value="index" :disabled="item.disabled"></el-option>
			            </el-select>
			          </el-form-item>
			        </el-col>
			        <el-col :span="8" :xs="12">
			          <el-form-item label="预约日期" prop="appointDate">
			            <el-date-picker v-model="formData.appointDate" id="appointDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
			            	:picker-options="appointDateOptions"
			              :style="{width: '100%'}"></el-date-picker>
			          </el-form-item>
			        </el-col>
			        <el-col :span="8" :xs="12">
			          <el-form-item label="预约时间段" prop="appointTimeDesc">
			            <el-select v-model="formData.appointTimeDesc" id="appointTimeDesc" clearable :style="{width: '100%'}">
			              <el-option v-for="(item, index) in appointTimeDescOptions" :key="index" :label="item"
			                :value="index" :disabled="item.disabled"></el-option>
			            </el-select>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-red" label="产品用途" prop="contactOrderProductUse">
			            <el-select v-model="formData.contactOrderProductUse" id="contactOrderProductUse" placeholder="请选择产品用途" clearable
			              :style="{width: '100%'}" @change="orderfunc.changeContactOrderProductUse()">
			              <el-option v-for="(item, index) in contactOrderProductUseOptions" :key="index" :label="item"
			                :value="index" :disabled="item.disabled"></el-option>
			            </el-select>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item class="color-red" label="数量" prop="productAmount">
			            <el-input-number v-model="formData.productAmount" id="productAmount" :min="1" ></el-input-number>
			          </el-form-item>
			        </el-col>
			        <el-col :span="6" :xs="12">
			          <el-form-item label="来源单号" prop="sourceOrderCode">
			            <el-input v-model="formData.sourceOrderCode" id="sourceOrderCode" clearable :style="{width: '100%'}"></el-input>
			          </el-form-item>
			        </el-col>
			        <el-col :span="24" :xs="24">
			          <el-form-item label="备注">
			            <el-input v-model="formData.pubRemark" id="pubRemark" type="textarea" :autosize="{minRows: 4, maxRows: 4}"
			              :style="{width: '100%'}"></el-input>
			          </el-form-item>
			        </el-col>
		        </el-row>
		      </el-collapse-item>
		      <el-collapse-item title="其他信息" name="3">
		      <el-row>
		        <el-col :span="8" :xs="12">
		          <el-form-item label="购买日期" >
		            <el-date-picker v-model="formData.contactOrderBuyDate" id="contactOrderBuyDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
		              :style="{width: '100%'}" clearable></el-date-picker>
		          </el-form-item>
		        </el-col>
		        <el-col :span="8" :xs="12">
		          <el-form-item label="购买渠道" prop="proBuyWay">
		          	<el-select v-model="formData.contactOrderBuyChannel" id="contactOrderBuyChannel" clearable :style="{width: '100%'}">
		            	<el-option v-for="(item, index) in contactOrderBuyChannelOptions" :key="index" :label="item"
		                :value="index" :disabled="item.disabled"></el-option>
		            </el-select>
		          </el-form-item>
		        </el-col>
		        <el-col :span="8" :xs="12">
		          <el-form-item label="销售单位">
					<el-input v-model="formData.contactOrderSaleUnitName" id="contactOrderSaleUnitName" :style="{width: '100%'}">
		              <el-button slot="append" icon="el-icon-search" @click="orderpopup.shop()"></el-button>
		            </el-input>
		          </el-form-item>
		        </el-col>
		        <el-col :span="8" :xs="12">
		          <el-form-item label="指定网点" prop="unitName">
					<el-input v-model="formData.unitName" id="unitName" :style="{width: '100%'}">
		              <el-button slot="append" icon="el-icon-search" @click="orderpopup.website()"></el-button>
		            </el-input>
		          </el-form-item>
		        </el-col>
		        <el-col :span="8" :xs="12">
		          <el-form-item label="投诉等级" prop="complaintLevel">
		            <el-select v-model="formData.complaintLevel" id="complaintLevel" clearable :style="{width: '100%'}">
		            	<el-option v-for="(item, index) in complaintLevelOptions" :key="index" :label="item"
		                :value="index" :disabled="item.disabled"></el-option>
		            </el-select>
		          </el-form-item>
		        </el-col>
		        <el-col :span="8" :xs="12">
		          <el-form-item label="紧急程度" >
		          	<el-select v-model="formData.urgentLevel" id="urgentLevel" clearable :style="{width: '100%'}">
		            	<el-option v-for="(item, index) in urgentLevelOptions" :key="index" :label="item"
		                :value="index" :disabled="item.disabled"></el-option>
		            </el-select>
		          </el-form-item>
		        </el-col>	        
		        <el-col :span="24" :xs="24" v-show="neworderOnlineAgent">
		          <el-form-item label="转全网结果" prop="turnWebDepict">
		            <el-input v-model="formData.turnWebDepict" id="turnWebDepict" type="textarea"
		              :autosize="{minRows: 2, maxRows: 4}" :style="{width: '100%'}"></el-input>
		          </el-form-item>
		        </el-col>
		      </el-row>
		      </el-collapse-item>
		      <el-row>
		      	<el-col :span="4" :xs="24">
		          <el-form-item label="线下催单" prop="isHurry" style="width:75px">
		            <el-switch v-model="formData.isHurry" id="isHurry" active-color="#00C10A" :active-text="formData.contactCallNum==0?'':'来电'+formData.contactCallNum+'次'" st></el-switch>
		          </el-form-item>
		        </el-col>
		        <el-col :span="4" :xs="12">
		          <el-form-item label="通知中心" prop="isNotifyBranch" style="width:75px">
		            <el-switch v-model="formData.isNotifyBranch" id="isNotifyBranch" active-color="#00C10A"></el-switch>
		          </el-form-item>
		        </el-col>
		        <el-col :span="4" :xs="12">
		          <el-form-item label="手工升级" prop="manualUpgradeFlag" style="width:75px">
		            <el-switch v-model="formData.manualUpgradeFlag" id="manualUpgradeFlag" active-color="#00C10A"></el-switch>
		          </el-form-item>
		        </el-col>
		      </el-row>
		      <el-row>
		        <el-col :span="6" :xs="7">
		            <el-button type="success" size="mini" @click="submitCSS('formData')" :disabled="submitCSSBtn"> 提交售后工单 </el-button>
		        </el-col>
		        <el-col :span="6" :xs="4">
		            <el-button type="primary" size="mini" @click="saveLocal('formData')"> 暂存 </el-button>
		        </el-col>
		        <el-col :span="6" :xs="7">
		            <el-button type="primary" size="mini" @click="submitService"> 产品建议与服务 </el-button>
		        </el-col>
		        <el-col :span="6" :xs="6">
		            <el-button type="success" size="mini" @click="submitOnline('formData')" :disabled="submitOnlineBtn"> 提交小结单 </el-button>
		        </el-col>
		      </el-row>
		      </el-collapse>
		    </el-form>
		    
		</div>
	</div>
	
	<script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
	<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
	<script type="text/javascript" src="/wecom/static/js/vue/vue.min.js"></script>
	<script type="text/javascript" src="/wecom/static/js/element/element-ui.js"></script>
	<script type="text/javascript" src="/wecom/static/js/common/common.js?v=20220530"></script>
	<script type="text/javascript" src="/wecom/static/js/order/order-vue.js?v=20250805"></script>
	<script type="text/javascript" src="/wecom/static/js/order/neworder.js?v=20250805"></script>
	<script type="text/javascript" src="/wecom/static/js/order/neworder.layer.js?v=20250805"></script>
	<script type="text/javascript" src="/wecom/static/js/time.js?v=20220530"></script>
	<script>
	
		//userid
		var custUserId = "";
		$(function(){
			setTimeout(function(){
				$("input,select").on("change",function(){
					orderfunc.showToolAlert(this,event);
				});
				$("textarea").on("change",function(){
					orderfunc.showToolAlert(this,event);
					orderfunc.showCheckAlert(this.id,this.value);
				});
			},1500);
		});
	</script>
</body>
</html>