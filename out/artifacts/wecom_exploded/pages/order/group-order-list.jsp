<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>群工单列表</title>
    <style>
        #menuContent {display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
        <div class="ibox">
                <%--搜索栏--%>
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5><span class="glyphicon glyphicon-list"></span> 群工单</h5>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">群名称</span>
                        <input class="form-control input-sm" name="GROUP_CHAT_NAME" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">建群管家</span>
                        <input class="form-control input-sm" name="GROUP_CREATE_STEWARD" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">稽查时间</span>
                        <input type="text" name="START_TIME" id="startDate" style="width: 180px;"   class="form-control input-sm" style="width:110px" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                        <span class="input-group-addon">-</span>
                        <input type="text" name="END_TIME" id="endDate"  style="width: 180px;" class="form-control input-sm" style="width:110px" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">稽查人</span>
                        <input class="form-control input-sm" name="CHECK_NAME" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">创建人</span>
                        <input class="form-control input-sm" name="CREATE_USER" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">服务工单</span>
                        <input class="form-control input-sm" name="SERVICE_ORDER_NUMBER" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">创建时间</span>
                        <input type="text" name="CREATE_START_TIME" id="startDate1" style="width: 180px;"   class="form-control input-sm" style="width:110px" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate1\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                        <span class="input-group-addon">-</span>
                        <input type="text" name="CREATE_END_TIME" id="endDate1"  style="width: 180px;" class="form-control input-sm" style="width:110px" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate1\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">当前进度</span>
                        <select name="ORDER_STATE"  class="form-control input-sm"data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('ORDER_STATE')" style="width:140px">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">群状态</span>
                        <select name="GROUP_STATE" class="form-control input-sm"data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('GROUP_STATE')" style="width:140px">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="GroupOrders.reload()">
                            <span class="glyphicon glyphicon-search"></span> 搜索</button>
                    </div>
                </div>
            </div>

                <%--表单展示--%>
            <div class="ibox-content">
                <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="GroupOrders.list">
                    <thead>
                    <tr>
                        <th class="text-c">群聊名称</th>
                        <th class="text-c">建群日期</th>
                        <th class="text-c">建群管家</th>
                        <th class="text-c">建群账号</th>
                        <th class="text-c">网点编码</th>
                        <th class="text-c">网点名称</th>
                        <th class="text-c">网点对接人</th>
                        <th class="text-c">所属运中</th>
                        <th class="text-c">用户姓名</th>
                        <th class="text-c">用户电话</th>
                        <th class="text-c">服务工单</th>
                        <th class="text-c">用户地址</th>
                        <th class="text-c">产品数量</th>
                        <th class="text-c">产品主体</th>
                        <th class="text-c">产品品牌</th>
                        <th class="text-c">产品品类</th>
                        <th class="text-c">产品型号</th>
                        <th class="text-c">销售单位</th>
                        <th class="text-c">销售人</th>
                        <th class="text-c">销售电话</th>
                        <th class="text-c">当前进度</th>
                        <th class="text-c">是否规范</th>
                        <th class="text-c">不规范原因</th>
                        <th class="text-c">稽查时间</th>
                        <th class="text-c">稽查人</th>
                        <th class="text-c">群状态</th>
                        <th class="text-c">备注</th>
                        <th class="text-c">业务属性</th>
                        <th class="text-c">创建时间</th>
                        <th class="text-c">创建人</th>
                        <th class="text-c">修改时间</th>
                        <th class="text-c">修改人</th>
                        <th class="text-c">会话记录查看与质检</th>
                        <th class="text-c">修改工单</th>
                    </tr>
                    </thead>
                    <tbody id="dataList">
                    </tbody>
                </table>
                <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:GROUP_CHAT_NAME}}</td>
										    <td>{{:GROUP_CREATE_TIME}}</td>
										    <td>{{:GROUP_CREATE_STEWARD}}</td>
										    <td>{{:GROUP_CREATE_ACC}}</td>
										    <td>{{:SITE_CODE}}</td>
										    <td>{{:SITE_NAME}}</td>
										    <td>{{:SITE_CONTACT}}</td>
										    <td>{{:AREA_CODE}}</td>
										    <td>{{:USER_NAME}}</td>
										    <td>{{:USER_PHONE}}</td>
										    <td>{{:SERVICE_ORDER_NUMBER}}</td>
										    <td>{{:USER_ADDRESS}}</td>
										    <td>{{:PRODUCT_COUNT}}</td>
										    <td>{{:ORG_CODE}}</td>
										    <td>{{:BRAND_NAME}}</td>
										    <td>{{:PROD_NAME}}</td>
										    <td>{{:PRODUCT_MODEL}}</td>
										    <td>{{:SALE_UNIT}}</td>
										    <td>{{:SALE_PEOPLE}}</td>
										    <td>{{:SALE_PHONE}}</td>
										    <td>{{dictFUN:ORDER_STATE 'ORDER_STATE'}}</td>
										    <td>{{dictFUN:IS_NORMS 'IS_NORMS'}}</td>
										    <td>{{dictFUN:REASON 'NONSTANDARD_REASON'}}</td>
										    <td>{{:CHECK_TIME}}</td>
										    <td>{{:CHECK_NAME}}</td>
										    <td>{{dictFUN:GROUP_STATE 'GROUP_STATE'}}</td>
										    <td>{{:BACKUP}}</td>
										    <td>{{dictFUN:ORDER_SOURCE 'ORDER_SOURCE'}}</td>
										    <td>{{:CREATE_TIME}}</td>
										    <td>{{:CREATE_USER}}</td>
										    <td>{{:UPDATE_TIME}}</td>
										    <td>{{:UPDATE_USER}}</td>
										<td>
                                          <a href="javascript:GroupOrders.detailed('{{:CHAT_ID}}')">查看</a>
<%--                                          &nbsp;<a href="javascript:GroupOrders.editData('{{:ID}}')">质检</a>--%>
                                        </td>
                                        <td>
                                           <a href="javascript:GroupOrders.editData('{{:ID}}')">修改</a>
                                        </td>
									    </tr>
								   {{/for}}
							 </script>

                <div class="row paginate" id="page">
                    <jsp:include page="/pages/common/pagination.jsp">
                        <jsp:param value="25" name="pageSize"/>
                    </jsp:include>
                </div>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript">
        jQuery.namespace("GroupOrders");
        $(function() {
            $("#searchForm").render({});
        });
        GroupOrders.detailed = function(chatId){
            popup.layerShow({type:2,title:'查看',area:['900px','900px'],offset:'20px'},"${ctxPath}/pages/chatrecord/zjzxtoMeiti.jsp",{sessionId:chatId});
        }

        //修改工单
        GroupOrders.editData = function(id){
            popup.layerShow({type:2,title:'修改',area:['700px','700px'],offset:'20px'},"${ctxPath}/pages/order/group-order-edit.html",{ID:id});
        }
        GroupOrders.reload= function(){
            $("#searchForm").searchData();
        }
        GroupOrders.del=function(id){
            var ids=[id];
            if(confirm("确认要删除吗？")){
                ajax.remoteCall("${ctxPath}/servlet/Orders?action=delete",{ids:ids},function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon: 1});
                        GroupOrders.reload();
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            }
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>