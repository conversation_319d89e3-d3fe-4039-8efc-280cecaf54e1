-- 取ownerName 坐席的
2025-08-12 16:53:38,738 INFO [mixgw:mixgw_mcsp] 请求参数：{"restParams":{"groupI1d":"","wechatGroupId":"wrPAQDcQAAQf7N-3goyu7wCdLrkDDCLA","enterpriseWechatId":"200"}}  at com.yunqu.cc.mixgw.inf.McspService.getGroupDetail(McspService.java:501)
2025-08-12 16:53:38,742 INFO [mixgw:mixgw_mcsp] 获取群聊基本信息,url=https://mcsp-api-sit.midea.com/api/rms_api/wec-backstage/wechat/external/customerGroup/getBaseInfo,SYNC_WECHAT_USER_APPKEY：91f45727f4ac4c5aaba25fbe3e2b964b SYNC_WECHAT_USER_SECRET 9deb183305de4fe188b3a527457fa6a3  at com.yunqu.cc.mixgw.inf.McspService.getGroupDetail(McspService.java:504)
2025-08-12 16:53:38,843 INFO [mixgw:mixgw_mcsp] POST请求返回结果 resp：{"code":"000000","msg":"操作成功","msgTemp":"","errorList":["000000:操作成功"],"traceId":"","data":{"groupId":"8631942021982142464","wechatGroupId":"wrPAQDcQAAQf7N-3goyu7wCdLrkDDCLA","name":"7.15测试","buildDate":"2025-07-11 17:19:13","owner":"13715683094","ownerName":""},"service":null,"level":0}  at com.yunqu.cc.mixgw.inf.McspService.getGroupDetail(McspService.java:508)
2025-08-12 16:53:38,843 INFO [mixgw:mixgw_mcsp] 查询接口成功~~  at com.yunqu.cc.mixgw.inf.McspService.getGroupDetail(McspService.java:511)

--取name 客户的
2025-08-12 16:55:21,612 INFO [undefine:wecom] 获取外部联系人信息接口返回{"errcode":0,"follow_user":[{"createtime":1752225540,"remark_mobiles":[],"description":"","remark":"石白","oper_userid":"13715683094","userid":"13715683094","add_way":2,"tags":[]}],"errmsg":"ok","external_contact":{"external_userid":"wmPAQDcQAA71VMwAh5gGnfCaYgb6lZgQ","unionid":"o4Zz5wqwOjugmJ9hiEB8XFtslSbM","gender":1,"name":"石白","avatar":"http://wx.qlogo.cn/mmhead/3GVibSSFicL1aJiczsD3lhz2bviaH1DhF42pmK9Nf1geCL3tv1zJYiaImh530Qf0iaYr9Jz4oKg7rsuVw/0","type":1}}  at com.yunqu.cc.wecom.servlet.ConfigServlet.actionForGetExtCustInfo(ConfigServlet.java:106)