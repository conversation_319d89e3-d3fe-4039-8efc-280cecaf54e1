package com.yunqu.cc.wecom.base;

import com.yq.busi.common.util.ConfigUtil;

/**
 * 常量
 *
 */
public class Constants {

	public final static String DS_NAME = "default-ds";//默认数据源名称
	public final static String DS_YW = "yw-ds";//默认数据源名称
	public final static String DS_MARS ="mars-ds";//mars数据源
	public final static String DS_YCBUSI = "ycbusi-ds";//平台数据源名称
	public final static String DS_YCUSER = "ycuser-ds";//平台数据源名称
	public final static String APP_NAME = "wecom";//应用

	/**
	 * 企业微信同步会话
	 */
	public final static String CC_WECOM_SYNC_SESSION = "CC_WECOM_SYNC_SESSION";
	
	/**
	 * 企微OAUTH2地址
	 */
	public final static String WECOM_OAUTH2_URL = ConfigUtil.getString(APP_NAME,"WECOM_OAUTH2_URL","https://open.weixin.qq.com/connect/oauth2/authorize?appid=CORPID&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect");
	/**
	 * 企微企业的CorpID
	 */
	public final static String WECOM_CORPID = ConfigUtil.getString("mixgw","WORK_WEIXIN_CORPID","wwc56202ba0a29b2fb");
	/**
	 * 企微登录之后跳转的地址
	 */
	public final static String WECOM_LOGIN_REDIRECT = ConfigUtil.getString(APP_NAME,"WECOM_LOGIN_REDIRECT","https://ccuat.midea.com/wecom/pages/index.html");
	/**
	 * 企微侧边栏应用id
	 */
	public final static String WECOM_AGENT_ID = ConfigUtil.getString(APP_NAME,"WECOM_AGENT_ID","1000005");
	/**
	 * 环境域名
	 */
	public final static String MIDEA_WEBSITE = ConfigUtil.getString(APP_NAME,"MIDEA_WEBSITE","https://ccuat.midea.com/");

	/**
	 * 企微-企业id
	 */
	public static String  getEnterpriseWechatId() {
		return ConfigUtil.getString(APP_NAME,"ENTERPRISE_WECHAT_ID","100");
	}
	
	/**
	 * 登录状态 0登入 1登出
	 */
	public final static String LOGIN_STATUS = "0";
	public final static String LOGOUT_STATUS = "1";
	
	/**
	 * 企微登录用户记录userId标记
	 */
	public final static String WECOM_OAUTH2_FLAG = "WECOM_OAUTH2_FLAG";
	
	/**
	 * 企微侧边栏渠道key
	 */
	public final static String WECOM_CHANNEL_KEY = "wecom_sidebar";

	/**
	 * 企微提交群工单缓存key
	 */
	public final static String WECOM_SUBMIT_KEY = "WECOM_SUBMIT_KEY";

}
