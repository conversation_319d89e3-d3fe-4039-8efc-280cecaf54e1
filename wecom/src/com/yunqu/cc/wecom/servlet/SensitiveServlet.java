package com.yunqu.cc.wecom.servlet;


import com.yunqu.cc.wecom.base.AppBaseServlet;
import com.yunqu.cc.wecom.model.SensitiveModel;
import org.easitline.common.core.web.EasyResult;

import javax.servlet.annotation.WebServlet;

//@WebServlet("/servlet/SensitiveServlet")
public class SensitiveServlet extends AppBaseServlet {

    public EasyResult actionForAdd() {
        String triggerTime = getPara("TRIGGER_TIME"); // 消息触发时间
        String roomId = getPara("ROOM_ID");
        String wxAccountName = getPara("WX_ACCOUNT_NAME");
        String wxAccountId = getPara("WX_ACCOUNT_ID");
        String keyWord = getPara("KEY_WORD");
        SensitiveModel sensitiveModel = new SensitiveModel();
        sensitiveModel.setRoomId(roomId);
        sensitiveModel.setWxAccountName(wxAccountName);
        sensitiveModel.setWxAccountId(wxAccountId);
        sensitiveModel.setKeyWord(keyWord);
        // 根据wxAccountId判断是否为手机号，如果是手机号则isAgent为1，否则为0
        String isAgent = (wxAccountId != null && wxAccountId.matches("^[0-9]{11}$")) ? "1" : "0";
        return sensitiveModel.saveSensitiveModel(sensitiveModel, triggerTime,isAgent);
    }


}