package com.yunqu.cc.wecom.model;

import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.wecom.base.AppBaseServlet;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class SensitiveModel extends AppBaseServlet {

    private String id;              // 主键ID
    private String roomId;          // 企微群聊ID
    private String roomName;        // 群聊名称
    private String wxAccountName;   // 触发企微账号名称
    private String wxAccountId;     // 触发企微账号ID
    private String agentAcc;        // 坐席登录账号
    private String agentName;       // 坐席姓名
    private String keyWord;         // 触发的敏感词
    private String triggerTime;     // 消息触发时间
    private String createTime;      // 记录创建时间

    // Getters and Setters

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getWxAccountName() {
        return wxAccountName;
    }

    public void setWxAccountName(String wxAccountName) {
        this.wxAccountName = wxAccountName;
    }

    public String getWxAccountId() {
        return wxAccountId;
    }

    public void setWxAccountId(String wxAccountId) {
        this.wxAccountId = wxAccountId;
    }

    public String getAgentAcc() {
        return agentAcc;
    }

    public void setAgentAcc(String agentAcc) {
        this.agentAcc = agentAcc;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getTriggerTime() {
        return triggerTime;
    }

    public void setTriggerTime(String triggerTime) {
        this.triggerTime = triggerTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * 保存敏感词触发记录。
     * <p>
     * 该方法根据传入的 {@link SensitiveModel} 对象和消息触发时间（triggerTime），查询匹配的坐席账号和姓名，
     * 并将相关信息保存到数据库表 {@code C_NO_WECOM_GROUP_TRIGGER_RECORD} 中。
     *
     * @param sensitiveModel 敏感词触发记录模型，包含群聊ID、群聊名称、触发企微账号信息、敏感词等（<b>必需</b>）。
     *                       - {@code roomId}: 企微群聊ID（示例值：wrtPgCCQAAE6JMBi2IwgLENT0QTT3pEg）
     *                       - {@code wxAccountName}: 触发企微账号名称（示例值：Account）
     *                       - {@code wxAccountId}: 触发企微账号ID（示例值：37）
     *                       - {@code keyWord}: 触发的敏感词（示例值：sb）
     * @param triggerTime    消息触发时间（格式：yyyy-MM-dd HH:mm:ss，<b>必需</b>，示例值：2022-10-13 08:03:09）。
     *                       用于匹配坐席登录记录的时间范围（triggerTime 前一天内）。
     * @return {@link EasyResult} 对象：
     *         - 如果成功保存记录，返回 {@code EasyResult.ok()}。
     *         - 如果发生异常（如数据库操作失败），返回 {@code EasyResult.fail()} 并记录错误日志。
     */
    public EasyResult saveSensitiveModel(SensitiveModel sensitiveModel, String triggerTime, String isAgent) {
        // 群聊id
        String roomId = sensitiveModel.getRoomId();
        String threeDaysAgo = DateUtil.addDay("yyyy-MM-dd HH:mm:ss", triggerTime, -1);
        // 用消息触发时间去匹配坐席账号和坐席名字
        if (StringUtils.isNotBlank(triggerTime)) {
            EasySQL sql = new EasySQL();
            // 根据群工单去获取坐席信息
            sql.append(roomId,"SELECT * FROM YWDB.C_NO_GROUP_ORDERS WHERE 1 = 1 AND CHAT_ID = ?");
            sql.append(triggerTime,"and CREATE_TIME <= ?");
            sql.append(threeDaysAgo,"and CREATE_TIME >= ?");
            try {
                List<EasyRow> loginRecords = getQuery().queryForList(sql.getSQL(), sql.getParams());
                EasyRow bestMatch = null;
                long minDiff = Long.MAX_VALUE;
                String agentAcc = "";
                String agentName = "";
                if (loginRecords != null && loginRecords.size()> 0) {
                    for (EasyRow row : loginRecords) {
                        String createTimeStr = row.getColumnValue("CREATE_TIME");
                        // 计算当前记录与 triggerTime 的时间差（单位：秒）
                        long diffSeconds = Math.abs(getSecondsBetween(createTimeStr, triggerTime, "yyyy-MM-dd HH:mm:ss"));
                        if (diffSeconds < minDiff) {
                            minDiff = diffSeconds;
                            bestMatch = row;
                        }
                    }
                }
                if (bestMatch != null) {
                     agentAcc = bestMatch.getColumnValue("UPDATE_USER");
                     if (StringUtils.isBlank(agentAcc)) {
                         agentAcc = bestMatch.getColumnValue("CREATE_USER");
                     }
                    EasySQL agentNameSql = new EasySQL();
                    agentNameSql.append(agentAcc,"SELECT USER_NAME FROM YWDB.C_YG_EMPLOYEE WHERE 1 = 1 AND USER_ACC = ?");
                     agentName = getQuery().queryForString(agentNameSql.getSQL(), agentNameSql.getParams());
                }
                // 后续保存敏感词记录时使用 bestMatch 的信息
                EasyRecord record = new EasyRecord("C_NO_WECOM_GROUP_TRIGGER_RECORD");
                // 通过roomId查询群聊名称
                record.set("ID", RandomKit.randomStr());
                record.set("ROOM_ID", roomId);
                record.set("ROOM_NAME", sensitiveModel.getRoomName() );
                record.set("WX_ACCOUNT_NAME", sensitiveModel.getWxAccountName());
                record.set("WX_ACCOUNT_ID", sensitiveModel.getWxAccountId());
                record.set("KEY_WORD", sensitiveModel.getKeyWord());
                record.set("TRIGGER_TIME", triggerTime);
                record.set("AGENT_ACC", agentAcc);
                record.set("AGENT_NAME", agentName);
                record.set("IS_AGENT", isAgent);
                record.set("CREATE_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                getQuery().save(record);

            } catch (Exception e) {
                getLogger().info("坐席敏感词保存失败" + e.getMessage());
                return  EasyResult.fail();
            }
        }
        return EasyResult.ok();
    }

    /**
     * 获取两个时间之间的秒数差值
     * @param dateStr1 第一个时间字符串
     * @param dateStr2 第二个时间字符串
     * @param pattern  时间格式
     * @return 两个时间之间的秒数差值
     */
    private long getSecondsBetween(String dateStr1, String dateStr2, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            Date date1 = sdf.parse(dateStr1);
            Date date2 = sdf.parse(dateStr2);

            // 计算两个时间之间的毫秒差值
            long diffMillis = Math.abs(date1.getTime() - date2.getTime());

            // 转换为秒
            return diffMillis / 1000;
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid date format", e);
        }
    }

}
