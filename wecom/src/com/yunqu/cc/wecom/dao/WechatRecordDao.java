package com.yunqu.cc.wecom.dao;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.wecom.base.AppDaoContext;
import com.yunqu.cc.wecom.base.CommonLogger;

@WebObject(name = "wechatRecordDao")
public class WechatRecordDao extends AppDaoContext{
	private Logger logger = CommonLogger.logger;
	
	
	@WebControl(name = "getWechatRecordList",type = Types.LIST)
	public JSONObject getWechatRecordList() {
		String keyWord = param.getString("KEY_WORD");
		String sender = param.getString("SENDER");
		EasySQL sql = new EasySQL();
		if(StringUtils.isBlank(keyWord)){
			sql = new EasySQL("SELECT t1.NAME,t2.ROOM_ID, t3.IS_TRIGGER_SENSITIVE FROM C_NO_WECOM_GROUP_USER_INFO t1 ");
			sql.append("LEFT JOIN C_NO_WECOM_GROUP_RECORD t2 ON t1.WECHAT_GROUP_ID =t2.ROOM_ID ");
			sql.append("LEFT JOIN C_NO_GROUP_ORDERS t3 ON t3.CHAT_ID = t2.ROOM_ID where 1=1");
			sql.append(param.getString("SEND_START_TIME"),"and t2.MSG_TIME >=?");
			sql.append(param.getString("SEND_END_TIME"),"and t2.MSG_TIME <=?");
			sql.append(param.getString("AGENT_AREA"),"and T2.AGENT_AREA = ?");
			sql.append(param.getString("AGENT_DEPT"),"and T2.AGENT_DEPT = ?");
			// 添加是否触发敏感词
			sql.append(param.getString("IS_TRIGGER_SENSITIVE"), "and t3.IS_TRIGGER_SENSITIVE = ?");
			if(StringUtils.isNotBlank(sender)){
				sql.append(sender,"and ( t2.SENDER = ?");
				sql.append(sender," or t2.AGENT_ACC = ?");
				sql.append(sender," or t1.USER_NAME = ?");
				sql.append(sender," or t1.USER_MOBLIE = ?");
				sql.append(sender," or t1.WECHAT_USER_ID = ?");
				sql.append(sender," or t1.USER_ID = ?");
				sql.append(sender," or t2.AGENT_NAME = ?)");
			}
			sql.appendLike(param.getString("NAME"),"and T1.NAME like ?");
			sql.append(" GROUP BY t2.ROOM_ID,t1.NAME,t3.IS_TRIGGER_SENSITIVE ");
			logger.info(1);
			/*sql.append("select DISTINCT t1.ROOM_ID ,T1.MSG_ID ,T1.SENDER ,T1.AGENT_ACC,T1.MSG_TIME ,T1.MSG_CONTENT ,T1.AGENT_NAME ,T1.CREATE_TIME ,T1.AGENT_AREA ,T1.AGENT_DEPT ,"
					+ "t2.NAME,t2.USER_TYPE,t2.USER_NAME, t3.IS_TRIGGER_SENSITIVE from C_NO_WECOM_GROUP_RECORD t1 LEFT JOIN C_NO_WECOM_GROUP_USER_INFO t2 ON t1.ROOM_ID  = t2.WECHAT_GROUP_ID ");
			sql.append("LEFT JOIN C_NO_GROUP_ORDERS t3 ON t3.CHAT_ID = t1.ROOM_ID ");
			sql.append("AND t1.SENDER  = t2.WECHAT_USER_ID");
			sql.append("where 1 = 1");
			sql.append(param.getString("SEND_START_TIME"),"and t1.MSG_TIME >=?");
			sql.append(param.getString("SEND_END_TIME"),"and t1.MSG_TIME <=?");

			if(StringUtils.isNotBlank(sender)){
				sql.append(sender,"and ( t1.SENDER = ?");
				sql.append(sender," or t1.AGENT_ACC = ?");
				sql.append(sender," or t2.USER_NAME = ?");
				sql.append(sender," or t2.USER_MOBLIE = ?");
				sql.append(sender," or t2.WECHAT_USER_ID = ?");
				sql.append(sender," or t2.USER_ID = ?");
				sql.append(sender," or t1.AGENT_NAME = ?)");
			}
			sql.append(param.getString("AGENT_AREA"),"and T1.AGENT_AREA = ?");
			sql.append(param.getString("AGENT_DEPT"),"and T1.AGENT_DEPT = ?");
			sql.append(param.getString("IS_TRIGGER_SENSITIVE"), "and t3.IS_TRIGGER_SENSITIVE = ?");
			sql.appendLike(param.getString("NAME"),"and T2.NAME like ?");*/
		}else{
			logger.info(2);
			sql.append("select DISTINCT t1.ROOM_ID ,T1.MSG_ID ,T1.SENDER ,T1.AGENT_ACC,T1.MSG_TIME ,T1.MSG_CONTENT ,T1.AGENT_NAME ,T1.CREATE_TIME ,T1.AGENT_AREA ,T1.AGENT_DEPT ,"
					+ "t2.NAME,t2.USER_TYPE,t2.USER_NAME, t1.IS_TRIGGER_SENSITIVE from C_NO_WECOM_GROUP_RECORD t1 LEFT JOIN C_NO_WECOM_GROUP_USER_INFO t2 ON t1.ROOM_ID  = t2.WECHAT_GROUP_ID ");
			sql.append("AND t1.SENDER  = t2.WECHAT_USER_ID");
		/*	sql.append("LEFT JOIN C_NO_GROUP_ORDERS t3 ON t3.CHAT_ID = t1.ROOM_ID ");*/
			sql.append("where 1 = 1");
			sql.append(param.getString("SEND_START_TIME"),"and t1.MSG_TIME >=?");
			sql.append(param.getString("SEND_END_TIME"),"and t1.MSG_TIME <=?");
			String[] keyWords = keyWord.split("#");
			if(keyWords.length==1){
				sql.appendLike(keyWord,"and t1.MSG_CONTENT like ?");
			}else if(keyWords.length>1){
				sql.append("and(");
				for (int i = 0; i < keyWords.length; i++) {
					if(i==0){
						sql.appendLike(keyWords[i]," t1.MSG_CONTENT like ?");
					}else{
						sql.appendLike(keyWords[i]," or t1.MSG_CONTENT like ?");
					}
					
				}
				sql.append(")");
			}
			if(StringUtils.isNotBlank(sender)){
				sql.append(sender,"and ( t1.SENDER = ?");
				sql.append(sender," or t1.AGENT_ACC = ?");
				sql.append(sender," or t2.USER_NAME = ?");
				sql.append(sender," or t2.USER_MOBLIE = ?");
				sql.append(sender," or t2.WECHAT_USER_ID = ?");
				sql.append(sender," or t2.USER_ID = ?");
				sql.append(sender," or t1.AGENT_NAME = ?)");
			}
			sql.append(param.getString("AGENT_AREA"),"and T1.AGENT_AREA = ?");
			sql.append(param.getString("AGENT_DEPT"),"and T1.AGENT_DEPT = ?");
			sql.append(param.getString("IS_TRIGGER_SENSITIVE"), "and t3.IS_TRIGGER_SENSITIVE = ?");
			sql.appendLike(param.getString("NAME"),"and T2.NAME like ?");
			sql.append("order by T1.MSG_TIME desc");
		}
		logger.info("sql"+sql.getSQL()+"params[]"+JSONObject.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(),sql.getParams());
		
	}
	
	public static void main(String[] args){
		String a = "123,23,43677,8732,423,53,6";
		String[] aa = a.split(",");
		System.out.println(aa.length);
		System.out.println(aa[6]);
		System.out.println(aa[0]);
		System.out.println(aa[0]);
		System.out.println(aa[0]);
	}
}
