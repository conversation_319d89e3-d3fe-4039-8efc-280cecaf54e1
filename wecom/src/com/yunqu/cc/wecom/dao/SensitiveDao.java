package com.yunqu.cc.wecom.dao;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.wecom.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

/**
 * 存放各模块通用的数据到层方法，每个模块的都需要用类似的类；
 * 命名格式为：模块名+CommonDao
 */
@WebObject(name = "sensitive")
public class SensitiveDao extends AppDaoContext {

    @WebControl(name = "getList", type = Types.LIST)
    public JSONObject getList() {
        EasySQL sql = new EasySQL();

        sql.append("SELECT * ");
        sql.append("FROM YWDB.C_NO_WECOM_GROUP_TRIGGER_RECORD ");
        sql.append("WHERE 1 = 1");
        sql.append(param.getString("startTime"), "AND TRIGGER_TIME >= ?");
        sql.append(param.getString("endTime"), "AND TRIGGER_TIME <= ?");
        sql.append(param.getString("isAgent"), "AND IS_AGENT = ?");
        sql.append("ORDER BY TRIGGER_TIME DESC");
        getLogger().info("查询敏感词列表：" + sql.getSQL() + "[params]" + sql.getParams());
		return this.queryForPageList(sql.getSQL(), sql.getParams());
    }

}
