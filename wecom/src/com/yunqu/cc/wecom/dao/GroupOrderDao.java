package com.yunqu.cc.wecom.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.wecom.base.AppDaoContext;
import com.yunqu.cc.wecom.base.CommonLogger;
import com.yunqu.cc.wecom.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.List;

@WebObject(name = "GroupOrders")
public class GroupOrderDao extends AppDaoContext {
    //创建日志
    private final static Logger logger = CommonLogger.getLogger();

    // yw_db数据源
    private static final EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);

    /**
     * 获取群工单列表
     *
     */
    @WebControl(name = "list",type = Types.LIST)
    public JSONObject getList() {
        EasySQL sql = this.getEasySQL("select T1.ID,T1.CHAT_ID,T1.GROUP_CHAT_NAME,T1.GROUP_CREATE_TIME,T1.GROUP_CREATE_STEWARD,T1.GROUP_CREATE_ACC,");
        sql.append("T1.SITE_CODE,T1.SITE_NAME,T1.SITE_CONTACT,T1.AREA_CODE,T1.USER_NAME,T1.USER_PHONE,T1.USER_ADDRESS,T1.ORG_CODE,T1.BRAND_NAME,");
        sql.append("T1.PROD_NAME,T1.PRODUCT_MODEL,T1.SALE_UNIT,T1.SALE_PEOPLE,T1.SALE_PHONE,T1.ORDER_STATE,T1.IS_NORMS,");
        sql.append("T1.REASON,T1.CHECK_TIME,T1.BACKUP,T1.ORDER_SOURCE,T1.CREATE_TIME,T1.CREATE_USER,T1.CHECK_NAME,T1.GROUP_STATE,T1.SERVICE_ORDER_NUMBER,");
        sql.append("COUNT(T2.GROUP_ORDER_ID) as PRODUCT_COUNT from  C_NO_GROUP_ORDERS T1"); //聚合函数获取产品数量
        sql.append("LEFT JOIN C_NO_GROUP_ORDERS_PRODUCT T2 ON T1.ID = T2.GROUP_ORDER_ID");
        sql.append("where 1=1");
        sql.appendLike(param.getString("GROUP_CHAT_NAME"),"and T1.GROUP_CHAT_NAME like ?");
        sql.appendLike(param.getString("GROUP_CREATE_STEWARD"),"and T1.GROUP_CREATE_STEWARD like ?");
        sql.append(param.getString("START_TIME"),"and T1.CHECK_TIME >= ?");
        sql.append(param.getString("END_TIME"),"and T1.CHECK_TIME <= ?");
        sql.append(param.getString("CREATE_START_TIME"),"and T1.CREATE_TIME >= ?");
        sql.append(param.getString("CREATE_END_TIME"),"and T1.CREATE_TIME <= ?");
        sql.appendLike(param.getString("CHECK_NAME"),"and T1.CHECK_NAME like ?");
        sql.appendLike(param.getString("CREATE_USER"),"and T1.CREATE_USER like ?");
        sql.append(param.getString("ORDER_STATE"),"and T1.ORDER_STATE = ?");
        sql.append(param.getString("GROUP_STATE"),"and T1.GROUP_STATE = ?");
        sql.appendLike(param.getString("SERVICE_ORDER_NUMBER"),"and T1.SERVICE_ORDER_NUMBER like ?");
        sql.append("GROUP BY T1.ID,T1.CHAT_ID,T1.GROUP_CHAT_NAME,T1.GROUP_CREATE_TIME,T1.GROUP_CREATE_STEWARD,T1.GROUP_CREATE_ACC, T1.SITE_CODE,T1.SITE_NAME,T1.SITE_CONTACT,T1.AREA_CODE,T1.USER_NAME,T1.USER_PHONE,T1.USER_ADDRESS,T1.ORG_CODE,T1.BRAND_NAME, T1.PROD_NAME,T1.PRODUCT_MODEL,T1.SALE_UNIT,T1.SALE_PEOPLE,T1.SALE_PHONE,T1.IS_NORMS, T1.REASON,T1.CHECK_TIME,T1.BACKUP,T1.ORDER_SOURCE,T1.ORDER_STATE,T1.CREATE_TIME,T1.CREATE_USER,T1.CHECK_NAME,T1.GROUP_STATE,T1.SERVICE_ORDER_NUMBER"); //聚合函数必须配合GROUP BY使用
        sql.append(" ORDER BY T1.CREATE_TIME DESC ");
        logger.info("获取群工单列表sql：" + sql.getSQL() + ",参数为:" + sql.getParams());
        return queryForPageList(sql.getSQL(), sql.getParams(),null);
    }


    /**
     * 根据Id 获取群工单信息
     *
     */
    @WebControl(name = "getGroupOrderById",type = Types.RECORD)
    public JSONObject getGroupOrderById() throws SQLException {
        JSONObject result = new JSONObject();
        String id = this.param.getString("ID");
        logger.info("id:" + id);
        if ("".equals(id)){
            result.put("status",0);
            result.put("msg","查询不到对应群工单记录");
            return result;
        }
        try {
            EasySQL sql = this.getEasySQL("select  * from  C_NO_GROUP_ORDERS");
            sql.append("where ID = ?");
            result = queryForRecord(sql.getSQL(), new Object[]{id}, null);
            EasySQL orderSql = this.getEasySQL("select ORG_CODE as orgCode,BRAND_CODE as brandCode,BRAND_NAME as brandName,PROD_CODE as prodCode,PROD_NAME as prodName,PRODUCT_CODE as productCode,PRODUCT_MODEL as productModel from  C_NO_GROUP_ORDERS_PRODUCT");
            orderSql.append("where GROUP_ORDER_ID = ?");
            //传递数组格式
            JSONObject list = queryForList(orderSql.getSQL(), new Object[]{id}, new JSONMapperImpl() {
                @Override
                public JSONObject mapRow(ResultSet rs, int rowNum) {
                    try {
                        ResultSetMetaData meta = rs.getMetaData();
                        int columnCount = meta.getColumnCount();
                        String[] column = new String[columnCount];
                        for (int i = 0; i < columnCount; i++){
                            column[i] = meta.getColumnLabel(i + 1);
                            if (i == 0){
                                column[i] = "orgCode";
                            }
                            if (i == 1){
                                column[i] = "brandCode";
                            }
                            if (i == 2){
                                column[i] = "brandName";
                            }
                            if (i == 3){
                                column[i] = "prodCode";
                            }
                            if (i == 4){
                                column[i] = "prodName";
                            }
                            if (i == 5){
                                column[i] = "productCode";
                            }
                            if (i == 6){
                                column[i] = "productModel";
                            }
                        }
                        JSONObject row = new JSONObject(true);
                        for (int j = 0; j < columnCount; j++) {
                            String value = rs.getString(j + 1);
                            if (value == null){
                                value = "";
                            }
                            row.put(column[j], value);
                        }
                        return row;
                    } catch (Exception ex) {
                        logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
                        return null;
                    }
                }
            });
            result.getJSONObject("data").put("productList",list.getJSONArray("data"));
            logger.info("result:" + result);
        }catch (Exception e){
            logger.error(CommonUtil.getClassNameAndMethod(this)+ "The information of the saved group ticket is abnormal:"+ e.getMessage(), e);
        }
        return result;
    }


    /**
     * 根据chatId 获取群工单信息
     *
     */
    @WebControl(name = "getGroupOrderByChatId",type = Types.RECORD)
    public JSONObject getGroupOrderByChatId() throws SQLException {
        JSONObject finalResult = new JSONObject();
        String chatId = this.param.getString("chatId");
        if ("".equals(chatId)){
            logger.info("群聊Id为空,查询不到对应群工单记录");
            return EasyResult.fail("群聊Id为空,查询不到对应群工单记录");
        }
        try {
            EasySQL sql = this.getEasySQL("select  * from  C_NO_GROUP_ORDERS");
            sql.append("where CHAT_ID = ?");
            JSONObject result = queryForRecord(sql.getSQL(), new Object[]{chatId}, null);
            if (result.getJSONObject("data").isEmpty()){
                logger.info("当前群聊: " + chatId + "查询不到对应群工单记录,调用美云销接口获取");
                return EasyResult.fail("查询不到对应群工单记录");
            }
            String orderId = result.getJSONObject("data").getString("ID");
            logger.info("orderId:" + orderId);
            EasySQL orderSql = this.getEasySQL("select ORG_CODE as orgCode,BRAND_CODE as brandCode,BRAND_NAME as brandName,PROD_CODE as prodCode,PROD_NAME as prodName,PRODUCT_CODE as productCode,PRODUCT_MODEL as productModel from  C_NO_GROUP_ORDERS_PRODUCT");
            orderSql.append("where GROUP_ORDER_ID = ?");
            //传递数组格式
            JSONObject list = queryForList(orderSql.getSQL(), new Object[]{orderId}, new JSONMapperImpl() {
                @Override
                public JSONObject mapRow(ResultSet rs, int rowNum) {
                    try {
                        ResultSetMetaData meta = rs.getMetaData();
                        int columnCount = meta.getColumnCount();
                        String[] column = new String[columnCount];
                        for (int i = 0; i < columnCount; i++){
                            column[i] = meta.getColumnLabel(i + 1);
                            if (i == 0){
                                column[i] = "orgCode";
                            }
                            if (i == 1){
                                column[i] = "brandCode";
                            }
                            if (i == 2){
                                column[i] = "brandName";
                            }
                            if (i == 3){
                                column[i] = "prodCode";
                            }
                            if (i == 4){
                                column[i] = "prodName";
                            }
                            if (i == 5){
                                column[i] = "productCode";
                            }
                            if (i == 6){
                                column[i] = "productModel";
                            }
                        }
                        JSONObject row = new JSONObject(true);
                        for (int j = 0; j < columnCount; j++) {
                            String value = rs.getString(j + 1);
                            if (value == null){
                                value = "";
                            }
                            row.put(column[j], value);
                        }
                        return row;
                    } catch (Exception ex) {
                        logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
                        return null;
                    }
                }
            });
            logger.info("list:" + list);
            JSONArray productList = list.getJSONArray("data");
            logger.info("当前群聊:" + chatId +"，记录的产品列表信息:"+ productList);
            finalResult = result.getJSONObject("data");
            finalResult.put("productList",productList);
            logger.info("finalResult:" + finalResult);
        }catch (Exception e){
            logger.error(CommonUtil.getClassNameAndMethod(this)+ "The information of the saved group ticket is abnormal:"+ e.getMessage(), e);
            EasyResult.fail("根据chatId查询群工单失败");
        }
        return EasyResult.ok(finalResult);
    }


    /**
     * 调用美云销接口获取群信息
     * @return
     */
    @WebControl(name = "getGroupDetail",type = Types.RECORD)
    public JSONObject getGroupDetail(){
        String chatId = this.param.getString("chatId");
        if ("".equals(chatId)){
            return EasyResult.error(0,"群聊chatId为空");
        }
        JSONObject result = new JSONObject();
        JSONObject request = new JSONObject();
        JSONObject data = new JSONObject();
        JSONObject restParams = new JSONObject();
        restParams.put("enterpriseWechatId", Constants.getEnterpriseWechatId()); //企业ID 美的：100,小天鹅:200
        restParams.put("groupI1d", "");
        restParams.put("wechatGroupId", chatId);
        data.put("restParams",restParams);
        request.put("command", "getGroupDetail");
        request.put("data", data);
        try {
            IService service = ServiceContext.getService("MIXGW_MCSP_INTEFACE");
            result = service.invoke(request);
            logger.info("wecomResult:" + result);
        } catch (ServiceException e) {
            logger.error("IService request failed, request parameters"+ JSON.toJSONString(request)+",原因"+e.getMessage());
        }
        return result;
    }

    /**
     * 返回进行提交群工单的账号信息
     * @return
     */
    @WebControl(name = "getCheckName",type = Types.RECORD)
    public JSONObject getCheckName(){
        JSONObject result = new JSONObject();
        String mip = this.getUserPrincipal().getUserInfo().getUserAcct();
        String name = this.getUserPrincipal().getUserInfo().getUsername();
        result.put("checkName",name+mip);
        return result;
    }
}
