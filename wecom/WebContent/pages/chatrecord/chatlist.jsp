<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
  <title>列表查询</title>
  <style type="text/css">
    a:link{ color:#00adff;}
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      background: transparent;
    }
    ::-webkit-scrollbar-track {
      background: transparent;
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background-color: #C1C1C1;
    }
    ::-webkit-scrollbar-thumb:hover {
      background-color: #A8A8A8;
    }
  </style>
</EasyTag:override>
<EasyTag:override name="content">
  <form action="" method="post" name="searchForm" class="form-inline"
        id="searchForm" onsubmit="return false" data-toggle="">
    <div class="ibox">
      <div class="ibox-title clearfix" id="divId">
        <div class="form-group">
          <div>
          <h5><span class="glyphicon glyphicon-list"></span> 聊天列表</h5>
          </div>
          <hr style="margin:5px -15px">
          <div class="input-group input-group-sm">
            <span class="input-group-addon">发送时间</span>
            <input type="text" class="form-control input-sm Wdate" id="getSendStartDate" name="SEND_START_TIME" style="width:150px" autocomplete="off" onclick="WdatePicker({onpicked:function(){this.blur();maxTime1('getSendStartDate','getSendEndDate')},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
            <span class="input-group-addon">~</span>
            <input type="text" class="form-control input-sm Wdate" id="getSendEndDate" name="SEND_END_TIME" style="width:150px" autocomplete="off" onclick="WdatePicker({minDate:'#F{$dp.$D(\'getSendStartDate\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
          </div>

         <div class="input-group">
	      <span class="input-group-addon">运营区域</span>	
			<select class="form-control input-sm" id="opAreaCode" name="AGENT_AREA" data-mars="peCommon.getDept" style="width:90px;">
				<option value="">请选择</option>
			</select>
		   </div>
		  <div class="input-group">
		      <span class="input-group-addon">坐席班组</span>	
				<select class="form-control input-sm" id="agentDept" name="AGENT_DEPT" style="width:105px;">
					<option value="">请选择</option>
				</select>
		   </div>

          <!-- <div class="input-group input-group-sm">
            <span class="input-group-addon">聊天类型</span>
            <select class="form-control input-sm" name="CHARTYPE"  data-mars="" onchange="">
              <option value="">请选择</option>
            </select>
          </div> -->
			<div class="input-group input-group-sm">
            <span class="input-group-addon">群名称</span>
            <input type="text" name="NAME" id="NAME" class="form-control input-sm" style="width: 120px" value="" placeholder="请输入群名称">
          </div>
          <div class="input-group input-group-sm">
            <span class="input-group-addon">发送人</span>
            <input type="text" name="SENDER" id="SENDER" class="form-control input-sm" style="width: 120px" value="" placeholder="请输入发送人">
          </div>
          <div class="input-group input-group-sm">
            <span class="input-group-addon">关键字</span>
            <input type="text" name="KEY_WORD" id="KEY_WORD" class="form-control input-sm" style="width: 300px" value="" placeholder="请输入关键词，同时多个关键词用#分割，例：A#B">
          </div>
          <div class="input-group input-group-sm">
            <span class="input-group-addon">是否触发敏感词</span>
            <select class="form-control input-sm" name="IS_TRIGGER_SENSITIVE" id="IS_TRIGGER_SENSITIVE" style="width: 100px">
              <option value="">请选择</option>
              <option value="1">是</option>
              <option value="0">否</option>
            </select>
          </div>
          <div class="input-group input-group-sm">
            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal"  onclick="chatlist.searchData('1')"><span class="glyphicon glyphicon-search"></span> <span>查询</span></button>
          </div>
          <div class="input-group ">
            <button type="button" class="btn btn-sm btn-default" onclick="chatlist.reset()"><span class="glyphicon glyphicon-repeat"></span><span>重置</span></button>
          </div>
        </div>
      </div>
     <div class="ibox-content table-responsive">
          	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="wechatRecordDao.getWechatRecordList">
                          <thead>
                       	 	<tr>
						      <th>序号</th>
						      <th>发送人</th>
					          <th>接收人</th>
					          <th>员工部门</th>
					          <th>发送时间</th>
				              <th>聊天内容</th>
                              <th>是否触发敏感词</th>
					          <th>操作</th>
  							</tr>
                          </thead>
                          <tbody id="dataList">
                          </tbody>
                </table>
               
                     	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td>{{:#index+1}}</td>
											<td>{{getSender:USER_TYPE AGENT_NAME SENDER USER_NAME fn='getSender'}}</td> 
											<td>{{:NAME}}</td> 
											<td>{{getSendDept:AGENT_DEPT fn='getSendDept'}}</td> 
											<td>{{getMsgTime:MSG_TIME fn='getMsgTime'}}</td> 
											<td>{{getMsgContent:MSG_CONTENT fn='getMsgContent'}}</td> 
                                            <td>{{getSensitive:IS_TRIGGER_SENSITIVE fn='getSensitive'}}</td>
									        <td>
												<a href="javascript:void(0)" onclick=" chatlist.editdetail('{{:ROOM_ID}}','{{:MSG_TIME}}','{{:MSG_ID}}')">查看会话</a>
                                                {{if IS_TRIGGER_SENSITIVE == '1'}}
                                                <a href="javascript:void(0)" onclick=" chatlist.viewSensitiveRecord('{{:ROOM_ID}}','{{:MSG_TIME}}','{{:MSG_ID}}')">查看触发记录</a>
                                                {{/if}}
											</td> 
										</tr>
								    {{/for}}					         
							 </script>
                   <div class="row paginate">
                   	<jsp:include page="/pages/common/pagination.jsp"/>
                   </div> 
            	</div> 
    </div>
  </form>
</EasyTag:override>
<EasyTag:override name="script">
  <script type="text/javascript">
    jQuery.namespace("chatlist");
    requreLib.setplugs('wdate')
    //初始化函数
    $(function() {
      $("#getSendStartDate").val(getYesterDayDate()+' 00:00:00')
      $("#getSendEndDate").val(getYesterDayDate()+' 23:59:59')
      $("#searchForm").render({
        
       });
     // chatlist.initData();
    })
  //根据字典编号获取字典
    $.views.converters('getSender', function(userType, agentName,sender,userName) {
        if(!chatlist.existkeyword()){
            return '/';
        }else {
            if(userType=='0'){
                return (agentName || userName) +"[群主]";
            }else {
                return (agentName || userName || sender) +"[群成员]";
            }
        }
    });
    
    $.views.converters('getSendDept', function(agentDept) {
        if(!chatlist.existkeyword()){
            return '/';
        }else {
            return custValFun(agentDept,"peCommon.getDept(5)");
        }
    });
    $.views.converters('getMsgTime', function(msgTime) {
        if(!chatlist.existkeyword()){
            return '/';
        }else {
            return msgTime;
        }
    });
    $.views.converters('getMsgContent', function(msgContent) {
        if(!chatlist.existkeyword()){
            return '/';
        }else {
            return  msgContent;
        }
    });
    $.views.converters('getSensitive', function(isSensitive) {
        return isSensitive == '1' ? '是' : '否';
        if(!chatlist.existkeyword()){
            return '/';
        }else {
        }
    });
    $('#opAreaCode').change(function() {
  		var value = $(this).val();
  		$('#agentDept').data('mars', 'peCommon.getDept("5", "'+ value +'")');
  		$('#agentDept').render();
  	});
   

    //查看会话 群id,当前消息发送时间
    chatlist.editdetail = function (roomId,msgTime,msgId){
    	msgTime = msgTime.substring(0,10);
    	popup.layerShow({
			type : 2,
			title : "查看群聊明细",
			offset : '20px',
			area : [ '800px', '700px' ],
			shadeClose:false
		},"${ctxPath}/pages/chatrecord/zjzxtoMeiti.jsp",{sessionId:roomId,sendTime:msgTime,msgId:msgId});
    }
    
    //查看触发敏感词记录
    chatlist.viewSensitiveRecord = function (roomId,msgTime,msgId){
    	popup.layerShow({
			type : 2,
			title : "查看触发敏感词记录",
			offset : '20px',
			area : [ '800px', '700px' ],
			shadeClose:false
		},"${ctxPath}/pages/chatrecord/sensitiveRecord.html",{roomId:roomId,msgTime:msgTime,msgId:msgId});
    }
    
    //重置
    chatlist.reset = function(){
      $("#divId select").val("");
      $("#divId input").val("");
    };

    //判断查询条件有无关键字
    chatlist.existkeyword = function (){
      if ($("#KEY_WORD").val()){
        return true;
      }
      return false;
    }

    //搜索数据
    chatlist.searchData = function(flag) {
    	
      if (!$("#SENDER").val() && !$("#agentDept").val() && !$("#getSendStartDate").val() ){
        layer.msg("发送时间/发送人/成员部门请至少输入一项",{icon: 5});
        return;
      }
  		$("#searchForm").searchData();
    }
    
    var custControlsData = {};
    function custValFun(val, contro, contextPath, params, callBack) {
    	var data = custControlsData[contro];
    	if (!data) {
    		data = getControlsData(contro, contextPath, params);
    		if (data.length == 0) {
    			contextPath = contextPath || '';
    			console.error('访问['+contextPath+'/'+contro+']获取不到数据');
    		}
    		data = data[contro].data;
    		custControlsData[contro] = data;
    	}
    	if (callBack) {
    		val = eval(callBack+'(val, data)');
    	} else {
    		for (var i in data) {
    			if (i == val) {
    				return data[i];
    			}
    		}
    	}
    	return val;
    }
    function getControlsData(contro, contextPath, params) {
    	var data = {};
    	if (params) {
    		params = eval('('+params+')');
    	} else {
    		params = {};
    	}
    	ajax.daoCall({
    		params: params,
    		controls: [contro]
    	}, function(result) {
    		data = result;
    	}, {
    		contextPath: contextPath,
    		async: false
    	});
    	return data;
    }
  </script>
  <script type="text/javascript">

    //限制开始时间不能大于结束时间
    function maxTime1(start,end){
      var startTime=$("#"+start).val();
      var endTime=$("#"+end).val();
      if(startTime!=""&&endTime!=""&&startTime>endTime){
        layer.msg("开始时间应小于结束时间",{icon: 5});
        var time =endTime.substring(0,10)+startTime.substring(10);
        $("#"+start).val(time);
      }
    }
  </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>